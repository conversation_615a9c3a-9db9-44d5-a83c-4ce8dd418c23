const mongoose = require('mongoose');
const Application = require('../models/application');
const SMEEntity = require('../models/sme-entity');
const SMEUser = require('../models/sme-user');
const bcrypt = require('bcryptjs');

const migrateToSMEEntities = async () => {
  console.log('=== Starting SME Entity Migration ===');
  
  try {
    // Step 1: Get all existing applications
    const applications = await Application.find({}).lean();
    console.log(`Found ${applications.length} applications to migrate`);
    
    if (applications.length === 0) {
      console.log('No applications found. Migration completed.');
      return;
    }
    
    // Step 2: Group applications by unique business entities
    const businessGroups = new Map();
    let duplicateCount = 0;
    
    applications.forEach(app => {
      // Create unique key based on CIPC registration number and legal name
      const cipcNumber = app.businessInfo?.cipcRegistrationNumber || 'unknown';
      const legalName = app.businessInfo?.legalName || 'unknown';
      const businessKey = `${cipcNumber}_${legalName}`.toLowerCase().replace(/\s+/g, '_');
      
      if (!businessGroups.has(businessKey)) {
        businessGroups.set(businessKey, {
          applications: [],
          businessInfo: app.businessInfo,
          personalInfo: app.personalInfo,
          contactDetails: app.contactDetails,
          financialInfo: app.financialInfo,
          bbbeeProfile: app.bbbeeProfile,
          programmes: new Set()
        });
      } else {
        duplicateCount++;
      }
      
      const group = businessGroups.get(businessKey);
      group.applications.push(app);
      
      // Track programmes this SME is involved in
      if (app.programmeId) {
        group.programmes.add(app.programmeId.toString());
      }
    });
    
    console.log(`Identified ${businessGroups.size} unique business entities`);
    console.log(`Found ${duplicateCount} duplicate business entities`);
    
    // Step 3: Create SME entities
    const smeEntityMap = new Map(); // Maps application IDs to SME entity IDs
    let createdEntities = 0;
    
    for (const [businessKey, group] of businessGroups) {
      try {
        // Validate required fields
        if (!group.businessInfo?.legalName || !group.businessInfo?.cipcRegistrationNumber) {
          console.warn(`Skipping business entity with missing required fields: ${businessKey}`);
          continue;
        }
        
        // Create programme registrations
        const programmeRegistrations = Array.from(group.programmes).map(progId => ({
          programmeId: new mongoose.Types.ObjectId(progId),
          registrationDate: new Date(),
          status: 'active',
          eligibilityStatus: 'pending_review'
        }));
        
        const smeEntity = new SMEEntity({
          businessRegistration: {
            legalName: group.businessInfo.legalName,
            tradingName: group.businessInfo.tradingName || group.businessInfo.legalName,
            registrationNumber: group.businessInfo.registrationNumber,
            cipcRegistrationNumber: group.businessInfo.cipcRegistrationNumber,
            entityType: group.businessInfo.entityType || 'Pty Ltd',
            startTradingDate: group.businessInfo.startTradingDate || new Date(),
            cipcRegistrationDocument: group.businessInfo.cipcRegistrationDocument,
            vatNumber: group.financialInfo?.vatRegistrationNumber,
            taxNumber: group.financialInfo?.taxNumber,
            industry: group.businessInfo.industry,
            businessType: group.businessInfo.businessType,
            yearEstablished: group.businessInfo.yearEstablished,
            employeeCount: group.businessInfo.employeeCount,
            website: group.businessInfo.website
          },
          primaryContact: {
            name: group.contactDetails?.name || group.personalInfo?.firstName || 'Unknown',
            surname: group.contactDetails?.surname || group.personalInfo?.lastName || 'Unknown',
            email: group.contactDetails?.email || group.personalInfo?.email || `unknown_${Date.now()}@example.com`,
            mainOfficeNumber: group.contactDetails?.mainOfficeNumber,
            cellphoneNumber: group.contactDetails?.cellphoneNumber || group.personalInfo?.phone || 'Unknown',
            position: group.contactDetails?.position || 'Owner'
          },
          businessAddress: group.businessInfo?.address || {},
          financialProfile: {
            annualTurnover: group.financialInfo?.annualTurnover,
            netProfit: group.financialInfo?.netProfit,
            currentAssets: group.financialInfo?.currentAssets,
            currentLiabilities: group.financialInfo?.currentLiabilities,
            totalDebt: group.financialInfo?.totalDebt,
            lastFinancialYear: group.financialInfo?.lastFinancialYear,
            taxClearanceExpiryDate: group.financialInfo?.taxClearanceExpiryDate,
            taxClearanceCertificateDocument: group.financialInfo?.taxClearanceCertificateDocument,
            vatRegistered: group.financialInfo?.vatRegistered || false,
            vatRegistrationNumber: group.financialInfo?.vatRegistrationNumber,
            vatCertificateDocument: group.financialInfo?.vatCertificateDocument
          },
          bbbeeProfile: group.bbbeeProfile || {},
          programmeRegistrations: programmeRegistrations,
          status: 'active',
          verificationStatus: {
            businessRegistration: true, // Assume existing data is verified
            contactDetails: true,
            financialInformation: !!group.financialInfo,
            bbbeeCompliance: !!group.bbbeeProfile,
            overallStatus: 'complete'
          },
          createdBy: 'migration_script',
          createdAt: new Date()
        });
        
        const savedEntity = await smeEntity.save();
        createdEntities++;
        
        // Map all applications in this group to the new SME entity
        group.applications.forEach(app => {
          smeEntityMap.set(app._id.toString(), savedEntity._id);
        });
        
        console.log(`Created SME Entity: ${savedEntity.id} for ${group.applications.length} applications`);
        
        // Create primary SME user for this entity
        await createPrimarySMEUser(savedEntity, group);
        
      } catch (error) {
        console.error(`Error creating SME entity for ${businessKey}:`, error.message);
      }
    }
    
    console.log(`Successfully created ${createdEntities} SME entities`);
    
    // Step 4: Update applications to reference SME entities
    let updatedApplications = 0;
    
    for (const [appId, smeEntityId] of smeEntityMap) {
      try {
        const result = await Application.findByIdAndUpdate(
          appId,
          {
            smeEntityId: smeEntityId,
            $unset: {
              personalInfo: 1,
              businessInfo: 1,
              contactDetails: 1,
              financialInfo: 1,
              bbbeeProfile: 1
            }
          },
          { new: true }
        );
        
        if (result) {
          updatedApplications++;
        }
      } catch (error) {
        console.error(`Error updating application ${appId}:`, error.message);
      }
    }
    
    console.log(`Successfully updated ${updatedApplications} applications`);
    
    // Step 5: Validation
    await validateMigration();
    
    console.log('=== SME Entity Migration Completed Successfully ===');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
};

const createPrimarySMEUser = async (smeEntity, group) => {
  try {
    // Generate username from business name
    const baseUsername = smeEntity.businessRegistration.tradingName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .substring(0, 20);
    
    let username = `${baseUsername}_admin`;
    let counter = 1;
    
    // Ensure unique username
    while (await SMEUser.findOne({ username })) {
      username = `${baseUsername}_admin_${counter}`;
      counter++;
    }
    
    // Generate temporary password
    const tempPassword = `Temp${Math.random().toString(36).substring(2, 10)}!`;
    
    const smeUser = new SMEUser({
      username,
      email: smeEntity.primaryContact.email,
      password: tempPassword, // Will be hashed by pre-save middleware
      firstName: smeEntity.primaryContact.name,
      lastName: smeEntity.primaryContact.surname,
      phone: smeEntity.primaryContact.cellphoneNumber,
      position: smeEntity.primaryContact.position,
      smeEntityId: smeEntity._id,
      smeRole: 'owner',
      permissions: [
        'create_applications',
        'edit_applications',
        'submit_applications',
        'view_applications',
        'edit_sme_profile',
        'manage_sme_users',
        'upload_documents',
        'view_financial_data',
        'edit_financial_data'
      ],
      status: 'active',
      emailVerified: false, // Will need to verify email
      createdBy: 'migration_script'
    });
    
    await smeUser.save();
    console.log(`Created SME User: ${smeUser.id} for entity: ${smeEntity.id}`);
    console.log(`Temporary password: ${tempPassword} (CHANGE IMMEDIATELY)`);
    
  } catch (error) {
    console.error(`Error creating SME user for entity ${smeEntity.id}:`, error.message);
  }
};

const validateMigration = async () => {
  console.log('=== Validating Migration ===');
  
  // Check SME entities
  const smeEntityCount = await SMEEntity.countDocuments();
  console.log(`Total SME entities created: ${smeEntityCount}`);
  
  // Check SME users
  const smeUserCount = await SMEUser.countDocuments();
  console.log(`Total SME users created: ${smeUserCount}`);
  
  // Check applications with SME references
  const appsWithSME = await Application.countDocuments({ smeEntityId: { $exists: true } });
  console.log(`Applications with SME entity references: ${appsWithSME}`);
  
  // Check for orphaned applications
  const orphanedApps = await Application.countDocuments({ 
    smeEntityId: { $exists: false },
    businessInfo: { $exists: true }
  });
  
  if (orphanedApps > 0) {
    console.warn(`WARNING: ${orphanedApps} applications still have embedded business info`);
  }
  
  // Validate data integrity
  const sampleApp = await Application.findOne({ smeEntityId: { $exists: true } })
    .populate('smeEntityId');
  
  if (sampleApp && sampleApp.smeEntityId) {
    console.log('✓ Sample application successfully references SME entity');
    console.log(`  SME: ${sampleApp.smeEntityId.businessRegistration.legalName}`);
  } else {
    console.error('✗ Sample application validation failed');
  }
  
  console.log('=== Migration Validation Complete ===');
};

// Export the migration function
module.exports = { migrateToSMEEntities, validateMigration };
