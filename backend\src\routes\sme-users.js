const express = require('express');
const router = express.Router();
const SMEUser = require('../models/sme-user');
const SMEEntity = require('../models/sme-entity');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { authenticateToken } = require('../middleware/auth');

// Helper function to get default permissions based on role
function getDefaultPermissions(role) {
  const permissionMap = {
    owner: [
      'create_applications',
      'edit_applications',
      'submit_applications',
      'view_applications',
      'edit_sme_profile',
      'manage_sme_users',
      'upload_documents',
      'view_financial_data',
      'edit_financial_data'
    ],
    director: [
      'create_applications',
      'edit_applications',
      'submit_applications',
      'view_applications',
      'edit_sme_profile',
      'upload_documents',
      'view_financial_data',
      'edit_financial_data'
    ],
    manager: [
      'create_applications',
      'edit_applications',
      'view_applications',
      'upload_documents',
      'view_financial_data'
    ],
    authorized_user: [
      'view_applications',
      'upload_documents'
    ],
    finance_manager: [
      'view_applications',
      'upload_documents',
      'view_financial_data',
      'edit_financial_data'
    ]
  };
  
  return permissionMap[role] || ['view_applications'];
}

// POST /api/sme-users/register - SME user registration
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, firstName, lastName, phone, position, smeEntityId, smeRole } = req.body;
    
    // Validate required fields
    if (!username || !email || !password || !firstName || !lastName || !smeEntityId || !smeRole) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Missing required fields',
          details: [
            { field: 'username', message: 'Username is required' },
            { field: 'email', message: 'Email is required' },
            { field: 'password', message: 'Password is required' },
            { field: 'firstName', message: 'First name is required' },
            { field: 'lastName', message: 'Last name is required' },
            { field: 'smeEntityId', message: 'SME entity ID is required' },
            { field: 'smeRole', message: 'SME role is required' }
          ].filter(item => !req.body[item.field.replace(/([A-Z])/g, '$1').toLowerCase()])
        }
      });
    }
    
    // Validate SME entity exists
    const smeEntity = await SMEEntity.findById(smeEntityId);
    if (!smeEntity) {
      return res.status(400).json({ 
        success: false,
        error: {
          code: 'INVALID_SME_ENTITY',
          message: 'Invalid SME entity'
        }
      });
    }
    
    // Check if user already exists
    const existingUser = await SMEUser.findOne({ $or: [{ username }, { email }] });
    if (existingUser) {
      return res.status(400).json({ 
        success: false,
        error: {
          code: 'USER_EXISTS',
          message: 'Username or email already exists'
        }
      });
    }
    
    // Generate email verification token
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');
    
    const smeUser = new SMEUser({
      username,
      email,
      password,
      firstName,
      lastName,
      phone,
      position,
      smeEntityId,
      smeRole,
      permissions: getDefaultPermissions(smeRole),
      emailVerificationToken,
      emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    });
    
    await smeUser.save();
    
    // TODO: Send verification email
    
    res.status(201).json({
      success: true,
      data: {
        message: 'User registered successfully. Please check your email for verification.',
        userId: smeUser.id
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(e => ({
        field: e.path,
        message: e.message,
        code: 'VALIDATION_ERROR',
        value: e.value
      }));
      
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Request validation failed',
          details: validationErrors
        }
      });
    }
    
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Registration failed',
        details: error.message
      }
    });
  }
});

// POST /api/sme-users/login - SME user login
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Username and password are required'
        }
      });
    }
    
    const user = await SMEUser.findOne({ username }).populate('smeEntityId');
    if (!user) {
      return res.status(401).json({ 
        success: false,
        error: {
          code: 'AUTHENTICATION_FAILED',
          message: 'Invalid credentials'
        }
      });
    }
    
    // Check if account is locked
    if (user.isLocked()) {
      return res.status(423).json({ 
        success: false,
        error: {
          code: 'ACCOUNT_LOCKED',
          message: 'Account temporarily locked due to too many failed login attempts'
        }
      });
    }
    
    const isValidPassword = await user.comparePassword(password);
    if (!isValidPassword) {
      await user.incLoginAttempts();
      return res.status(401).json({ 
        success: false,
        error: {
          code: 'AUTHENTICATION_FAILED',
          message: 'Invalid credentials'
        }
      });
    }
    
    // Reset login attempts on successful login
    if (user.loginAttempts > 0) {
      await user.resetLoginAttempts();
    }
    
    // Update last login
    user.lastLogin = new Date();
    await user.save();
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        smeEntityId: user.smeEntityId._id,
        userType: 'sme_user',
        permissions: user.permissions
      },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: '24h' }
    );
    
    res.json({
      success: true,
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          smeRole: user.smeRole,
          permissions: user.permissions,
          smeEntity: user.smeEntityId
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Login failed',
        details: error.message
      }
    });
  }
});

// GET /api/sme-users/profile - Get current user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = await SMEUser.findById(req.user.userId)
      .populate('smeEntityId')
      .select('-password');
    
    if (!user) {
      return res.status(404).json({ 
        success: false,
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: 'User not found'
        }
      });
    }
    
    res.json({
      success: true,
      data: user,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch profile',
        details: error.message
      }
    });
  }
});

// PUT /api/sme-users/profile - Update current user profile
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const allowedUpdates = ['firstName', 'lastName', 'phone', 'position'];
    const updates = {};
    
    // Only allow specific fields to be updated
    allowedUpdates.forEach(field => {
      if (req.body[field] !== undefined) {
        updates[field] = req.body[field];
      }
    });
    
    if (Object.keys(updates).length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'NO_UPDATES',
          message: 'No valid fields to update'
        }
      });
    }
    
    updates.updatedAt = new Date();
    updates.updatedBy = req.user.userId;
    
    const user = await SMEUser.findByIdAndUpdate(
      req.user.userId,
      updates,
      { new: true, runValidators: true }
    ).select('-password');
    
    if (!user) {
      return res.status(404).json({ 
        success: false,
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: 'User not found'
        }
      });
    }
    
    res.json({
      success: true,
      data: user,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(e => ({
        field: e.path,
        message: e.message,
        code: 'VALIDATION_ERROR',
        value: e.value
      }));
      
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Request validation failed',
          details: validationErrors
        }
      });
    }
    
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to update profile',
        details: error.message
      }
    });
  }
});

// POST /api/sme-users/change-password - Change password
router.post('/change-password', authenticateToken, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Current password and new password are required'
        }
      });
    }
    
    const user = await SMEUser.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ 
        success: false,
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: 'User not found'
        }
      });
    }
    
    // Verify current password
    const isValidPassword = await user.comparePassword(currentPassword);
    if (!isValidPassword) {
      return res.status(401).json({ 
        success: false,
        error: {
          code: 'AUTHENTICATION_FAILED',
          message: 'Current password is incorrect'
        }
      });
    }
    
    // Update password
    user.password = newPassword;
    user.updatedAt = new Date();
    user.updatedBy = req.user.userId;
    await user.save();
    
    res.json({
      success: true,
      data: {
        message: 'Password changed successfully'
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to change password',
        details: error.message
      }
    });
  }
});

// POST /api/sme-users/verify-email - Verify email address
router.post('/verify-email', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Verification token is required'
        }
      });
    }
    
    const user = await SMEUser.findOne({
      emailVerificationToken: token,
      emailVerificationExpires: { $gt: Date.now() }
    });
    
    if (!user) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid or expired verification token'
        }
      });
    }
    
    user.emailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    user.status = 'active';
    await user.save();
    
    res.json({
      success: true,
      data: {
        message: 'Email verified successfully'
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Email verification failed',
        details: error.message
      }
    });
  }
});

module.exports = router;
