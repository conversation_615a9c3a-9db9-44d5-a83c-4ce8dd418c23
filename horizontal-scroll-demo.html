<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Horizontal Scroll Demo - User Roles Table</title>
    <style>
        /* Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }

        /* Main container */
        .main-container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            flex-shrink: 0;
        }

        /* Content area */
        .content-area {
            flex: 1;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            overflow: hidden; /* Important: hide overflow on parent */
        }

        /* Horizontal scroll wrapper - THIS IS THE KEY */
        .horizontal-scroll-wrapper {
            width: 100%;
            height: 100%;
            overflow-x: scroll !important; /* Force horizontal scroll */
            overflow-y: auto;
            padding: 20px;
        }

        /* Force scrollbar to always show */
        .horizontal-scroll-wrapper::-webkit-scrollbar {
            height: 16px;
            background: #f1f1f1;
        }

        .horizontal-scroll-wrapper::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 8px;
        }

        .horizontal-scroll-wrapper::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Content that forces horizontal scroll */
        .wide-content {
            min-width: 2000px; /* This forces horizontal scrolling */
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* Table styling */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        th {
            background: #667eea;
            color: white;
        }

        /* Visual indicator */
        .scroll-indicator {
            background: #ff6b6b;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h3>Navigation</h3>
            <p>Dashboard</p>
            <p>User Roles</p>
            <p>Settings</p>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Horizontal Scroll Wrapper -->
            <div class="horizontal-scroll-wrapper">
                <!-- Wide Content -->
                <div class="wide-content">
                    <div class="scroll-indicator">
                        ← Scroll horizontally to see more content →
                    </div>
                    
                    <h1>User Roles & Permissions</h1>
                    <p>This content is 2000px wide, forcing a horizontal scrollbar to appear.</p>
                    
                    <table>
                        <thead>
                            <tr>
                                <th>Resource</th>
                                <th>Action</th>
                                <th>Admin</th>
                                <th>Manager</th>
                                <th>Officer</th>
                                <th>Reviewer</th>
                                <th>User</th>
                                <th>Applicant</th>
                                <th>Corp Reviewer</th>
                                <th>Corp Approver</th>
                                <th>SP Officer</th>
                                <th>SP Reviewer</th>
                                <th>SP Read-Only</th>
                                <th>SME Owner</th>
                                <th>SME Director</th>
                                <th>SME Manager</th>
                                <th>SME Finance</th>
                                <th>SME User</th>
                                <th>Extra Column 1</th>
                                <th>Extra Column 2</th>
                                <th>Extra Column 3</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Applications</td>
                                <td>View</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                            </tr>
                            <tr>
                                <td>Applications</td>
                                <td>Create</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✗</td>
                                <td>✗</td>
                                <td>✓</td>
                                <td>✗</td>
                                <td>✗</td>
                                <td>✗</td>
                                <td>✗</td>
                                <td>✗</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✓</td>
                                <td>✗</td>
                                <td>✗</td>
                                <td>✗</td>
                                <td>✗</td>
                                <td>✗</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div style="margin-top: 40px;">
                        <h2>Additional Content</h2>
                        <p>This demonstrates that the horizontal scrollbar appears at the bottom of the scroll wrapper.</p>
                        <p>The key is having a wrapper div with overflow-x: scroll and content that's wider than the viewport.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Log scroll information
        const scrollWrapper = document.querySelector('.horizontal-scroll-wrapper');
        const wideContent = document.querySelector('.wide-content');
        
        console.log('Wrapper width:', scrollWrapper.clientWidth);
        console.log('Content width:', wideContent.scrollWidth);
        console.log('Has horizontal scroll:', wideContent.scrollWidth > scrollWrapper.clientWidth);
    </script>
</body>
</html>