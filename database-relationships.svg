<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="2400" height="1800" viewBox="0 0 2400 1800">
  <defs>
    <!-- Define arrowhead markers -->
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#333"/>
    </marker>
    
    <!-- Define styles -->
    <style>
      .entity-box { stroke: #333; stroke-width: 2; }
      .entity-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: white; }
      .entity-field { font-family: Arial, sans-serif; font-size: 12px; fill: #333; }
      .relationship { stroke: #666; stroke-width: 2; fill: none; }
      .relationship-label { font-family: Arial, sans-serif; font-size: 11px; fill: #666; }
      
      /* Entity category colors */
      .core-entity { fill: #3498db; }
      .sme-entity { fill: #e74c3c; }
      .comm-entity { fill: #f39c12; }
      .financial-entity { fill: #27ae60; }
      .assessment-entity { fill: #9b59b6; }
      .support-entity { fill: #34495e; }
    </style>
  </defs>
  
  <!-- Title -->
  <text x="1200" y="30" text-anchor="middle" style="font-family: Arial, sans-serif; font-size: 24px; font-weight: bold;">Screening Portal Database Relationships</text>
  
  <!-- Legend -->
  <g id="legend" transform="translate(50, 50)">
    <text x="0" y="0" style="font-family: Arial, sans-serif; font-size: 16px; font-weight: bold;">Legend:</text>
    <rect x="0" y="10" width="20" height="15" class="core-entity"/>
    <text x="25" y="22" class="entity-field">Core Entities</text>
    <rect x="0" y="30" width="20" height="15" class="sme-entity"/>
    <text x="25" y="42" class="entity-field">SME Entities</text>
    <rect x="0" y="50" width="20" height="15" class="comm-entity"/>
    <text x="25" y="62" class="entity-field">Communication</text>
    <rect x="0" y="70" width="20" height="15" class="financial-entity"/>
    <text x="25" y="82" class="entity-field">Financial</text>
    <rect x="0" y="90" width="20" height="15" class="assessment-entity"/>
    <text x="25" y="102" class="entity-field">Assessment</text>
    <rect x="0" y="110" width="20" height="15" class="support-entity"/>
    <text x="25" y="122" class="entity-field">Supporting</text>
  </g>
  
  <!-- Core Entities -->
  
  <!-- CorporateSponsor -->
  <g id="corporate-sponsor" transform="translate(300, 200)">
    <rect width="200" height="150" class="entity-box core-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">CorporateSponsor</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">name: String</text>
    <text x="10" y="85" class="entity-field">registrationNumber: String</text>
    <text x="10" y="100" class="entity-field">contactEmail: String</text>
    <text x="10" y="115" class="entity-field">status: String</text>
    <text x="10" y="130" class="entity-field">createdAt: Date</text>
  </g>
  
  <!-- FundingProgramme -->
  <g id="funding-programme" transform="translate(600, 200)">
    <rect width="200" height="165" class="entity-box core-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">FundingProgramme</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">name: String</text>
    <text x="10" y="85" class="entity-field">description: String</text>
    <text x="10" y="100" class="entity-field">corporateSponsorId: ObjectId</text>
    <text x="10" y="115" class="entity-field">budget: Number</text>
    <text x="10" y="130" class="entity-field">startDate: Date</text>
    <text x="10" y="145" class="entity-field">endDate: Date</text>
  </g>
  
  <!-- Application -->
  <g id="application" transform="translate(900, 200)">
    <rect width="200" height="195" class="entity-box core-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">Application</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">applicationNumber: String</text>
    <text x="10" y="85" class="entity-field">fundingProgrammeId: ObjectId</text>
    <text x="10" y="100" class="entity-field">corporateSponsorId: ObjectId</text>
    <text x="10" y="115" class="entity-field">smeEntityId: ObjectId</text>
    <text x="10" y="130" class="entity-field">status: String</text>
    <text x="10" y="145" class="entity-field">stage: String</text>
    <text x="10" y="160" class="entity-field">submittedAt: Date</text>
    <text x="10" y="175" class="entity-field">data: Mixed</text>
  </g>
  
  <!-- User -->
  <g id="user" transform="translate(1200, 200)">
    <rect width="200" height="180" class="entity-box core-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">User</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">username: String</text>
    <text x="10" y="85" class="entity-field">email: String</text>
    <text x="10" y="100" class="entity-field">role: String</text>
    <text x="10" y="115" class="entity-field">corporateSponsorId: ObjectId</text>
    <text x="10" y="130" class="entity-field">fundingProgrammeId: ObjectId</text>
    <text x="10" y="145" class="entity-field">permissions: Array</text>
    <text x="10" y="160" class="entity-field">isActive: Boolean</text>
  </g>
  
  <!-- SME Entities -->
  
  <!-- SMEEntity -->
  <g id="sme-entity" transform="translate(300, 450)">
    <rect width="200" height="165" class="entity-box sme-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">SMEEntity</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">name: String</text>
    <text x="10" y="85" class="entity-field">registrationNumber: String</text>
    <text x="10" y="100" class="entity-field">taxNumber: String</text>
    <text x="10" y="115" class="entity-field">industry: String</text>
    <text x="10" y="130" class="entity-field">status: String</text>
    <text x="10" y="145" class="entity-field">createdAt: Date</text>
  </g>
  
  <!-- SMEUser -->
  <g id="sme-user" transform="translate(600, 450)">
    <rect width="200" height="150" class="entity-box sme-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">SMEUser</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">smeEntityId: ObjectId</text>
    <text x="10" y="85" class="entity-field">email: String</text>
    <text x="10" y="100" class="entity-field">firstName: String</text>
    <text x="10" y="115" class="entity-field">lastName: String</text>
    <text x="10" y="130" class="entity-field">role: String</text>
  </g>
  
  <!-- Communication Entities -->
  
  <!-- ChatRoom -->
  <g id="chat-room" transform="translate(1500, 200)">
    <rect width="200" height="135" class="entity-box comm-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">ChatRoom</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">name: String</text>
    <text x="10" y="85" class="entity-field">type: String</text>
    <text x="10" y="100" class="entity-field">applicationId: ObjectId</text>
    <text x="10" y="115" class="entity-field">createdAt: Date</text>
  </g>
  
  <!-- ChatMessage -->
  <g id="chat-message" transform="translate(1800, 200)">
    <rect width="200" height="150" class="entity-box comm-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">ChatMessage</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">chatRoomId: ObjectId</text>
    <text x="10" y="85" class="entity-field">userId: ObjectId</text>
    <text x="10" y="100" class="entity-field">message: String</text>
    <text x="10" y="115" class="entity-field">timestamp: Date</text>
    <text x="10" y="130" class="entity-field">attachments: Array</text>
  </g>
  
  <!-- ChatParticipant -->
  <g id="chat-participant" transform="translate(1650, 400)">
    <rect width="200" height="120" class="entity-box comm-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">ChatParticipant</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">chatRoomId: ObjectId</text>
    <text x="10" y="85" class="entity-field">userId: ObjectId</text>
    <text x="10" y="100" class="entity-field">joinedAt: Date</text>
  </g>
  
  <!-- Financial Entities -->
  
  <!-- LoanProduct -->
  <g id="loan-product" transform="translate(300, 700)">
    <rect width="200" height="150" class="entity-box financial-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">LoanProduct</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">name: String</text>
    <text x="10" y="85" class="entity-field">interestRate: Number</text>
    <text x="10" y="100" class="entity-field">termMonths: Number</text>
    <text x="10" y="115" class="entity-field">maxAmount: Number</text>
    <text x="10" y="130" class="entity-field">requirements: Array</text>
  </g>
  
  <!-- LoanOffer -->
  <g id="loan-offer" transform="translate(600, 700)">
    <rect width="200" height="165" class="entity-box financial-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">LoanOffer</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">applicationId: ObjectId</text>
    <text x="10" y="85" class="entity-field">loanProductId: ObjectId</text>
    <text x="10" y="100" class="entity-field">amount: Number</text>
    <text x="10" y="115" class="entity-field">status: String</text>
    <text x="10" y="130" class="entity-field">offeredAt: Date</text>
    <text x="10" y="145" class="entity-field">expiresAt: Date</text>
  </g>
  
  <!-- Loan -->
  <g id="loan" transform="translate(900, 700)">
    <rect width="200" height="180" class="entity-box financial-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">Loan</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">loanOfferId: ObjectId</text>
    <text x="10" y="85" class="entity-field">disbursedAmount: Number</text>
    <text x="10" y="100" class="entity-field">outstandingBalance: Number</text>
    <text x="10" y="115" class="entity-field">status: String</text>
    <text x="10" y="130" class="entity-field">disbursedAt: Date</text>
    <text x="10" y="145" class="entity-field">maturityDate: Date</text>
    <text x="10" y="160" class="entity-field">nextPaymentDate: Date</text>
  </g>
  
  <!-- Payment -->
  <g id="payment" transform="translate(1200, 700)">
    <rect width="200" height="150" class="entity-box financial-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">Payment</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">loanId: ObjectId</text>
    <text x="10" y="85" class="entity-field">amount: Number</text>
    <text x="10" y="100" class="entity-field">paymentDate: Date</text>
    <text x="10" y="115" class="entity-field">status: String</text>
    <text x="10" y="130" class="entity-field">reference: String</text>
  </g>
  
  <!-- Assessment Entities -->
  
  <!-- Scorecard -->
  <g id="scorecard" transform="translate(300, 950)">
    <rect width="200" height="150" class="entity-box assessment-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">Scorecard</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">applicationId: ObjectId</text>
    <text x="10" y="85" class="entity-field">templateId: ObjectId</text>
    <text x="10" y="100" class="entity-field">score: Number</text>
    <text x="10" y="115" class="entity-field">evaluatorId: ObjectId</text>
    <text x="10" y="130" class="entity-field">completedAt: Date</text>
  </g>
  
  <!-- Interview -->
  <g id="interview" transform="translate(600, 950)">
    <rect width="200" height="165" class="entity-box assessment-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">Interview</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">applicationId: ObjectId</text>
    <text x="10" y="85" class="entity-field">scheduledDate: Date</text>
    <text x="10" y="100" class="entity-field">interviewerId: ObjectId</text>
    <text x="10" y="115" class="entity-field">status: String</text>
    <text x="10" y="130" class="entity-field">notes: String</text>
    <text x="10" y="145" class="entity-field">rating: Number</text>
  </g>
  
  <!-- SiteVisit -->
  <g id="site-visit" transform="translate(900, 950)">
    <rect width="200" height="165" class="entity-box assessment-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">SiteVisit</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">applicationId: ObjectId</text>
    <text x="10" y="85" class="entity-field">visitDate: Date</text>
    <text x="10" y="100" class="entity-field">visitorId: ObjectId</text>
    <text x="10" y="115" class="entity-field">status: String</text>
    <text x="10" y="130" class="entity-field">findings: String</text>
    <text x="10" y="145" class="entity-field">photos: Array</text>
  </g>
  
  <!-- Supporting Entities -->
  
  <!-- ServiceProvider -->
  <g id="service-provider" transform="translate(1500, 600)">
    <rect width="200" height="135" class="entity-box support-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">ServiceProvider</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">name: String</text>
    <text x="10" y="85" class="entity-field">type: String</text>
    <text x="10" y="100" class="entity-field">services: Array</text>
    <text x="10" y="115" class="entity-field">status: String</text>
  </g>
  
  <!-- Role -->
  <g id="role" transform="translate(1800, 600)">
    <rect width="200" height="120" class="entity-box support-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">Role</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">name: String</text>
    <text x="10" y="85" class="entity-field">permissions: Array</text>
    <text x="10" y="100" class="entity-field">description: String</text>
  </g>
  
  <!-- Permission -->
  <g id="permission" transform="translate(2100, 600)">
    <rect width="200" height="120" class="entity-box support-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">Permission</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">name: String</text>
    <text x="10" y="85" class="entity-field">resource: String</text>
    <text x="10" y="100" class="entity-field">action: String</text>
  </g>
  
  <!-- Notification -->
  <g id="notification" transform="translate(1500, 800)">
    <rect width="200" height="150" class="entity-box support-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">Notification</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">userId: ObjectId</text>
    <text x="10" y="85" class="entity-field">type: String</text>
    <text x="10" y="100" class="entity-field">message: String</text>
    <text x="10" y="115" class="entity-field">read: Boolean</text>
    <text x="10" y="130" class="entity-field">createdAt: Date</text>
  </g>
  
  <!-- Report -->
  <g id="report" transform="translate(1800, 800)">
    <rect width="200" height="135" class="entity-box support-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">Report</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">name: String</text>
    <text x="10" y="85" class="entity-field">type: String</text>
    <text x="10" y="100" class="entity-field">parameters: Mixed</text>
    <text x="10" y="115" class="entity-field">generatedAt: Date</text>
  </g>
  
  <!-- Dashboard -->
  <g id="dashboard" transform="translate(2100, 800)">
    <rect width="200" height="135" class="entity-box support-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">Dashboard</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">userId: ObjectId</text>
    <text x="10" y="85" class="entity-field">widgets: Array</text>
    <text x="10" y="100" class="entity-field">layout: Mixed</text>
    <text x="10" y="115" class="entity-field">preferences: Mixed</text>
  </g>
  
  <!-- Workflow -->
  <g id="workflow" transform="translate(1200, 950)">
    <rect width="200" height="150" class="entity-box support-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">Workflow</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">name: String</text>
    <text x="10" y="85" class="entity-field">stages: Array</text>
    <text x="10" y="100" class="entity-field">transitions: Array</text>
    <text x="10" y="115" class="entity-field">programmeId: ObjectId</text>
    <text x="10" y="130" class="entity-field">isActive: Boolean</text>
  </g>
  
  <!-- CommitteeMeeting -->
  <g id="committee-meeting" transform="translate(1500, 1000)">
    <rect width="200" height="150" class="entity-box support-entity" rx="5"/>
    <text x="100" y="25" text-anchor="middle" class="entity-title">CommitteeMeeting</text>
    <line x1="10" y1="35" x2="190" y2="35" stroke="white" stroke-width="1"/>
    <text x="10" y="55" class="entity-field">_id: ObjectId</text>
    <text x="10" y="70" class="entity-field">programmeId: ObjectId</text>
    <text x="10" y="85" class="entity-field">meetingDate: Date</text>
    <text x="10" y="100" class="entity-field">attendees: Array</text>
    <text x="10" y="115" class="entity-field">decisions: Array</text>
    <text x="10" y="130" class="entity-field">status: String</text>
  </g>
  
  <!-- Relationships -->
  
  <!-- CorporateSponsor to FundingProgramme (1:N) -->
  <path d="M 500 275 L 600 275" class="relationship" marker-end="url(#arrow)"/>
  <text x="550" y="270" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- CorporateSponsor to User (1:N) -->
  <path d="M 500 290 Q 850 290 1200 290" class="relationship" marker-end="url(#arrow)"/>
  <text x="850" y="285" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- CorporateSponsor to Application (1:N) -->
  <path d="M 500 305 Q 700 305 900 305" class="relationship" marker-end="url(#arrow)"/>
  <text x="700" y="300" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- FundingProgramme to Application (1:N) -->
  <path d="M 800 282 L 900 282" class="relationship" marker-end="url(#arrow)"/>
  <text x="850" y="277" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- FundingProgramme to CommitteeMeeting (1:N) -->
  <path d="M 700 365 Q 700 1075 1500 1075" class="relationship" marker-end="url(#arrow)"/>
  <text x="1100" y="1070" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- FundingProgramme to User (1:N) -->
  <path d="M 800 300 Q 1000 300 1200 300" class="relationship" marker-end="url(#arrow)"/>
  <text x="1000" y="295" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- SMEEntity to SMEUser (1:N) -->
  <path d="M 500 532 L 600 532" class="relationship" marker-end="url(#arrow)"/>
  <text x="550" y="527" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- SMEEntity to Application (1:N) -->
  <path d="M 400 450 Q 400 420 1000 395" class="relationship" marker-end="url(#arrow)"/>
  <text x="700" y="415" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- Application to Scorecard (1:N) -->
  <path d="M 1000 395 Q 400 670 400 950" class="relationship" marker-end="url(#arrow)"/>
  <text x="700" y="670" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- Application to Interview (1:N) -->
  <path d="M 1000 395 Q 700 670 700 950" class="relationship" marker-end="url(#arrow)"/>
  <text x="850" y="670" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- Application to SiteVisit (1:N) -->
  <path d="M 1000 395 L 1000 950" class="relationship" marker-end="url(#arrow)"/>
  <text x="1020" y="670" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- Application to LoanOffer (N:1) -->
  <path d="M 1000 395 Q 700 550 700 700" class="relationship" marker-end="url(#arrow)"/>
  <text x="850" y="550" text-anchor="middle" class="relationship-label">N:1</text>
  
  <!-- Application to ChatRoom -->
  <path d="M 1100 297 Q 1300 297 1500 267" class="relationship" marker-end="url(#arrow)"/>
  <text x="1300" y="292" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- ChatRoom to ChatMessage (1:N) -->
  <path d="M 1700 267 L 1800 267" class="relationship" marker-end="url(#arrow)"/>
  <text x="1750" y="262" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- ChatRoom to ChatParticipant (1:N) -->
  <path d="M 1600 335 Q 1600 367 1650 460" class="relationship" marker-end="url(#arrow)"/>
  <text x="1625" y="400" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- User to ChatMessage (1:N) -->
  <path d="M 1400 290 Q 1600 290 1800 275" class="relationship" marker-end="url(#arrow)"/>
  <text x="1600" y="285" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- User to ChatParticipant (1:N) -->
  <path d="M 1400 320 Q 1525 320 1650 460" class="relationship" marker-end="url(#arrow)"/>
  <text x="1525" y="390" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- User to Notification (1:N) -->
  <path d="M 1300 380 Q 1300 590 1500 875" class="relationship" marker-end="url(#arrow)"/>
  <text x="1400" y="630" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- User to Dashboard (1:N) -->
  <path d="M 1400 350 Q 1750 350 2100 867" class="relationship" marker-end="url(#arrow)"/>
  <text x="1750" y="610" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- LoanProduct to LoanOffer (1:N) -->
  <path d="M 500 775 L 600 775" class="relationship" marker-end="url(#arrow)"/>
  <text x="550" y="770" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- LoanOffer to Loan (1:1) -->
  <path d="M 800 782 L 900 782" class="relationship" marker-end="url(#arrow)"/>
  <text x="850" y="777" text-anchor="middle" class="relationship-label">1:1</text>
  
  <!-- Loan to Payment (1:N) -->
  <path d="M 1100 790 L 1200 790" class="relationship" marker-end="url(#arrow)"/>
  <text x="1150" y="785" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- Role to Permission (N:M) -->
  <path d="M 2000 660 L 2100 660" class="relationship" marker-end="url(#arrow)"/>
  <text x="2050" y="655" text-anchor="middle" class="relationship-label">N:M</text>
  
  <!-- FundingProgramme to Workflow (1:N) -->
  <path d="M 700 365 Q 700 657 1200 1025" class="relationship" marker-end="url(#arrow)"/>
  <text x="950" y="695" text-anchor="middle" class="relationship-label">1:N</text>
  
  <!-- User roles and permissions -->
  <path d="M 1300 380 Q 1550 490 1800 660" class="relationship" marker-end="url(#arrow)"/>
  <text x="1550" y="520" text-anchor="middle" class="relationship-label">N:M</text>
  
  <!-- Additional labels for clarity -->
  <text x="400" y="430" text-anchor="middle" class="relationship-label" style="font-style: italic;">manages</text>
  <text x="1000" y="180" text-anchor="middle" class="relationship-label" style="font-style: italic;">submits</text>
  <text x="1600" y="180" text-anchor="middle" class="relationship-label" style="font-style: italic;">communicates</text>
  <text x="700" y="680" text-anchor="middle" class="relationship-label" style="font-style: italic;">finances</text>
  <text x="700" y="930" text-anchor="middle" class="relationship-label" style="font-style: italic;">assessments</text>
  
</svg>