const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const smeUserSchema = new mongoose.Schema({
  id: {
    type: String,
    unique: true
  },
  
  // Authentication Information
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 50
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: true,
    minlength: 8
  },
  
  // Personal Information
  firstName: { type: String, required: true, trim: true },
  lastName: { type: String, required: true, trim: true },
  phone: String,
  position: String,
  
  // SME Association
  smeEntityId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'SMEEntity', 
    required: true 
  },
  
  // Role within SME
  smeRole: {
    type: String,
    enum: ['owner', 'director', 'manager', 'authorized_user', 'finance_manager'],
    required: true
  },
  
  // Permissions within SME context
  permissions: [{
    type: String,
    enum: [
      'create_applications',
      'edit_applications',
      'submit_applications',
      'view_applications',
      'edit_sme_profile',
      'manage_sme_users',
      'upload_documents',
      'view_financial_data',
      'edit_financial_data'
    ]
  }],
  
  // Account Status
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'pending_verification'],
    default: 'pending_verification'
  },
  
  // Email Verification
  emailVerified: { type: Boolean, default: false },
  emailVerificationToken: String,
  emailVerificationExpires: Date,
  
  // Password Reset
  resetPasswordToken: String,
  resetPasswordExpires: Date,
  
  // Two-Factor Authentication (Future Enhancement)
  twoFactorEnabled: { type: Boolean, default: false },
  twoFactorSecret: String,
  
  // Login Tracking
  lastLogin: Date,
  loginAttempts: { type: Number, default: 0 },
  lockUntil: Date,
  
  // Audit Fields
  createdBy: String,
  createdAt: { type: Date, default: Date.now },
  updatedBy: String,
  updatedAt: { type: Date, default: Date.now }
});

// Pre-save middleware
smeUserSchema.pre('save', async function(next) {
  try {
    // Generate user ID if not present
    if (!this.id) {
      const count = await this.constructor.countDocuments();
      const nextNumber = (count + 1).toString().padStart(3, '0');
      this.id = `SMEU-2025-${nextNumber}`;
    }
    
    // Hash password if modified
    if (this.isModified('password')) {
      const salt = await bcrypt.genSalt(12);
      this.password = await bcrypt.hash(this.password, salt);
    }
    
    this.updatedAt = Date.now();
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare passwords
smeUserSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to check if account is locked
smeUserSchema.methods.isLocked = function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
};

// Method to increment login attempts
smeUserSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked()) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }
  
  return this.updateOne(updates);
};

// Method to reset login attempts
smeUserSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  });
};

// Indexes for performance
smeUserSchema.index({ username: 1 });
smeUserSchema.index({ email: 1 });
smeUserSchema.index({ smeEntityId: 1 });
smeUserSchema.index({ status: 1 });
smeUserSchema.index({ emailVerificationToken: 1 });
smeUserSchema.index({ resetPasswordToken: 1 });

const SMEUser = mongoose.model('SMEUser', smeUserSchema);

module.exports = SMEUser;
