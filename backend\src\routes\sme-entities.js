const express = require('express');
const router = express.Router();
const SMEEntity = require('../models/sme-entity');
const SMEUser = require('../models/sme-user');
const Application = require('../models/application');
const { authenticateToken, checkSMEAccess } = require('../middleware/auth');
const { 
  smeSecureRoute, 
  validateSMEEntityOwnership, 
  enhancedAddEntityFilter,
  validateEntityAssociation 
} = require('../middleware/enhanced-auth');

// GET /api/sme-entities - List SME entities with proper security filtering
router.get('/', ...smeSecureRoute, async (req, res) => {
  try {
    const { page = 1, limit = 25, status, search } = req.query;
    
    // Apply entity filter from security middleware
    const filter = { ...req.entityFilter };
    
    if (status) filter.status = status;
    if (search) {
      filter.$or = [
        { 'businessRegistration.legalName': new RegExp(search, 'i') },
        { 'businessRegistration.tradingName': new RegExp(search, 'i') },
        { 'businessRegistration.cipcRegistrationNumber': new RegExp(search, 'i') }
      ];
    }
    
    console.log('SME Entities GET: Applied security filter:', filter);
    
    const entities = await SMEEntity.find(filter)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('programmeRegistrations.programmeId')
      .sort({ createdAt: -1 });
    
    const total = await SMEEntity.countDocuments(filter);
    
    res.json({
      success: true,
      data: {
        entities,
        pagination: {
          page: Number(page),
          pageSize: Number(limit),
          totalItems: total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrevious: page > 1
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    console.error('SME Entities GET error:', error);
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch SME entities',
        details: process.env.NODE_ENV === 'development' ? error.message : 'Access denied'
      }
    });
  }
});

// POST /api/sme-entities - Create new SME entity
router.post('/', async (req, res) => {
  // Allow unauthenticated creation for registration process
  // If user is authenticated, use their info, otherwise allow public creation
  const isAuthenticated = req.headers.authorization && req.headers.authorization.startsWith('Bearer ');
  
  if (isAuthenticated) {
    // If authenticated, verify the token
    try {
      const { authenticateToken } = require('../middleware/auth');
      await new Promise((resolve, reject) => {
        authenticateToken(req, res, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    } catch (error) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_FAILED',
          message: 'Invalid authentication token'
        }
      });
    }
  }
  try {
    const smeEntity = new SMEEntity({
      ...req.body,
      createdBy: req.user ? (req.user.userId || req.user.id) : 'registration_system',
      status: 'pending_verification'
    });
    
    const savedEntity = await smeEntity.save();
    
    res.status(201).json({
      success: true,
      data: savedEntity,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(e => ({
        field: e.path,
        message: e.message,
        code: 'VALIDATION_ERROR',
        value: e.value
      }));
      
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Request validation failed',
          details: validationErrors
        }
      });
    }
    
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to create SME entity',
        details: error.message
      }
    });
  }
});

// GET /api/sme-entities/:id - Get SME entity details with enhanced security
router.get('/:id', ...smeSecureRoute, async (req, res) => {
  try {
    // Apply entity filter with specific ID
    const filter = { ...req.entityFilter, _id: req.params.id };
    
    console.log('SME Entity GET by ID: Applied security filter:', filter);
    
    const entity = await SMEEntity.findOne(filter)
      .populate('programmeRegistrations.programmeId');
    
    if (!entity) {
      return res.status(404).json({ 
        success: false,
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: 'SME entity not found or access denied',
          details: {
            resource: 'sme-entity',
            identifier: req.params.id
          }
        }
      });
    }
    
    res.json({
      success: true,
      data: entity,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    console.error('SME Entity GET by ID error:', error);
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch SME entity',
        details: process.env.NODE_ENV === 'development' ? error.message : 'Access denied'
      }
    });
  }
});

// PUT /api/sme-entities/:id - Update SME entity with enhanced security
router.put('/:id', ...smeSecureRoute, async (req, res) => {
  try {
    // Apply entity filter with specific ID for security
    const filter = { ...req.entityFilter, _id: req.params.id };
    
    console.log('SME Entity PUT: Applied security filter:', filter);
    
    // Check if user has edit permissions for SME users
    if (req.user.userType === 'sme_user') {
      if (!req.user.permissions?.includes('edit_sme_profile')) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Required permission: edit_sme_profile'
          }
        });
      }
    }
    
    const entity = await SMEEntity.findOneAndUpdate(
      filter,
      { 
        ...req.body, 
        updatedBy: req.user.userId || req.user.id,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    );
    
    if (!entity) {
      return res.status(404).json({ 
        success: false,
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: 'SME entity not found or access denied'
        }
      });
    }
    
    res.json({
      success: true,
      data: entity,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    console.error('SME Entity PUT error:', error);
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(e => ({
        field: e.path,
        message: e.message,
        code: 'VALIDATION_ERROR',
        value: e.value
      }));
      
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Request validation failed',
          details: validationErrors
        }
      });
    }
    
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to update SME entity',
        details: process.env.NODE_ENV === 'development' ? error.message : 'Access denied'
      }
    });
  }
});

// GET /api/sme-entities/:id/applications - Get applications for SME entity
router.get('/:id/applications', authenticateToken, async (req, res) => {
  try {
    // Check access permissions
    if (req.user.userType === 'sme_user' && req.user.smeEntityId !== req.params.id) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Access denied to this SME entity'
        }
      });
    }
    
    const applications = await Application.find({ smeEntityId: req.params.id })
      .populate('programmeId')
      .populate('corporateSponsorId')
      .sort({ createdAt: -1 });
    
    res.json({
      success: true,
      data: applications,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch applications',
        details: error.message
      }
    });
  }
});

// POST /api/sme-entities/:id/programme-registrations - Register for programme
router.post('/:id/programme-registrations', authenticateToken, async (req, res) => {
  try {
    const { programmeId } = req.body;
    
    // Check access permissions
    if (req.user.userType === 'sme_user') {
      if (req.user.smeEntityId !== req.params.id) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Access denied to this SME entity'
          }
        });
      }
      
      // Check if user has edit permissions
      if (!req.user.permissions.includes('edit_sme_profile')) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Required permission: edit_sme_profile'
          }
        });
      }
    }
    
    const entity = await SMEEntity.findById(req.params.id);
    if (!entity) {
      return res.status(404).json({ 
        success: false,
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: 'SME entity not found'
        }
      });
    }
    
    // Check if already registered
    const existingRegistration = entity.programmeRegistrations.find(
      reg => reg.programmeId.toString() === programmeId
    );
    
    if (existingRegistration) {
      return res.status(400).json({ 
        success: false,
        error: {
          code: 'DUPLICATE_REGISTRATION',
          message: 'Already registered for this programme'
        }
      });
    }
    
    entity.programmeRegistrations.push({
      programmeId,
      registrationDate: new Date(),
      status: 'active',
      eligibilityStatus: 'pending_review'
    });
    
    await entity.save();
    
    res.json({ 
      success: true,
      data: {
        message: 'Successfully registered for programme',
        entity
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to register for programme',
        details: error.message
      }
    });
  }
});

// GET /api/sme-entities/:id/users - Get SME users
router.get('/:id/users', authenticateToken, async (req, res) => {
  try {
    // Check access permissions
    if (req.user.userType === 'sme_user') {
      if (req.user.smeEntityId !== req.params.id) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Access denied to this SME entity'
          }
        });
      }
      
      // Check if user has manage users permission
      if (!req.user.permissions.includes('manage_sme_users')) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Required permission: manage_sme_users'
          }
        });
      }
    }
    
    const users = await SMEUser.find({ smeEntityId: req.params.id })
      .select('-password')
      .sort({ createdAt: -1 });
    
    res.json({
      success: true,
      data: users,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch SME users',
        details: error.message
      }
    });
  }
});

module.exports = router;
