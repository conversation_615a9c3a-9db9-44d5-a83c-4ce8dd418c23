const mongoose = require('mongoose');
const express = require('express');
const request = require('supertest');
require('dotenv').config();

async function testFundingProgrammesEndpoint() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-screening-app');
    
    const User = require('./src/models/user');
    const jwt = require('jsonwebtoken');
    
    console.log('=== TESTING FUNDING PROGRAMMES ENDPOINT ===');
    
    // Get the admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminUser) {
      console.log('❌ Admin user not found');
      return;
    }
    
    console.log('✅ Admin user found:', {
      id: adminUser._id,
      email: adminUser.email,
      roles: adminUser.roles,
      organizationType: adminUser.organizationType,
      organizationId: adminUser.organizationId,
      corporateSponsorId: adminUser.corporateSponsorId
    });
    
    // Create a JWT token for the admin user
    const tokenPayload = {
      id: adminUser._id.toString(),
      email: adminUser.email,
      roles: adminUser.roles,
      organizationType: adminUser.organizationType,
      organizationId: adminUser.organizationId,
      corporateSponsorId: adminUser.corporateSponsorId
    };
    
    const token = jwt.sign(tokenPayload, process.env.JWT_SECRET || 'your-secret-key', { expiresIn: '1h' });
    console.log('✅ JWT token created for admin user');
    
    // Create Express app with the funding programmes route
    const app = express();
    app.use(express.json());
    
    // Add the auth middleware
    const { authenticateToken } = require('./src/middleware/auth');
    
    // Add the funding programmes route
    const fundingProgrammesRouter = require('./src/routes/funding-programmes');
    app.use('/api/v1/funding-programmes', fundingProgrammesRouter);
    
    console.log('\n=== TESTING GET /api/v1/funding-programmes ===');
    
    // Test the endpoint
    const response = await request(app)
      .get('/api/v1/funding-programmes')
      .set('Authorization', `Bearer ${token}`)
      .expect((res) => {
        console.log('Response status:', res.status);
        console.log('Response body:', JSON.stringify(res.body, null, 2));
      });
    
    if (response.status === 200) {
      console.log('✅ Funding programmes endpoint working correctly!');
    } else {
      console.log('❌ Funding programmes endpoint returned error:', response.status);
    }
    
    await mongoose.disconnect();
  } catch (error) {
    console.error('❌ Test error:', error);
    process.exit(1);
  }
}

testFundingProgrammesEndpoint();
