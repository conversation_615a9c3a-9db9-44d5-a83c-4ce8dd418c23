/**
 * <PERSON><PERSON><PERSON> to update user organization types for testing sidebar filtering
 * 
 * This script helps you set different organization types for users to test
 * the sidebar organization type filtering functionality.
 * 
 * Usage:
 * 1. Connect to your MongoDB database
 * 2. Run this script in MongoDB shell or using Node.js
 * 3. Update the email addresses and organization types as needed
 */

// Connect to your database first, then run these commands:

// Example: Update specific users by email
db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { organizationType: "20/20Insight" } }
);

db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { organizationType: "CorporateSponsor" } }
);

db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { organizationType: "ServiceProvider" } }
);

db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { organizationType: "SmallMediumEnterprise" } }
);

// Check current organization types
console.log("Current user organization types:");
db.users.find({}, { email: 1, organizationType: 1, roles: 1 }).forEach(
  user => console.log(`${user.email}: ${user.organizationType} (roles: ${user.roles.join(', ')})`)
);

// Alternative: Update users by role patterns
// Update users with corporate roles
db.users.updateMany(
  { roles: { $in: ["corporate-sponsor-user", "CORPORATE_REVIEWER", "CORPORATE_APPROVER"] } },
  { $set: { organizationType: "CorporateSponsor" } }
);

// Update users with service provider roles
db.users.updateMany(
  { roles: { $in: ["SERVICE_PROVIDER_LOAN_OFFICER", "SERVICE_PROVIDER_REVIEWER", "SERVICE_PROVIDER_READ_ONLY"] } },
  { $set: { organizationType: "ServiceProvider" } }
);

// Update SME/applicant users
db.users.updateMany(
  { roles: { $in: ["SME_OWNER", "SME_MANAGER", "SME_EMPLOYEE", "SME_APPLICANT", "applicant"] } },
  { $set: { organizationType: "SmallMediumEnterprise" } }
);

// Keep admin users as 20/20Insight (they should already be this by default)
db.users.updateMany(
  { roles: { $in: ["admin", "manager", "System Administrator", "Manager"] } },
  { $set: { organizationType: "20/20Insight" } }
);

console.log("Organization types updated!");

// Verify the changes
console.log("\nUpdated user organization types:");
db.users.find({}, { email: 1, organizationType: 1, roles: 1 }).forEach(
  user => console.log(`${user.email}: ${user.organizationType} (roles: ${user.roles.join(', ')})`)
);
