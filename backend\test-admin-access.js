const mongoose = require('mongoose');
require('dotenv').config();

async function testAdminAccess() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-screening-app');
    
    const AccessControlService = require('./src/services/access-control.service');
    const User = require('./src/models/user');
    
    console.log('=== TESTING ADMIN ACCESS CONTROL ===');
    
    // Get the admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminUser) {
      console.log('❌ Admin user not found');
      return;
    }
    
    console.log('✅ Admin user found:');
    console.log(`   - ID: ${adminUser._id}`);
    console.log(`   - Name: ${adminUser.name}`);
    console.log(`   - Email: ${adminUser.email}`);
    console.log(`   - Roles: ${JSON.stringify(adminUser.roles)}`);
    console.log(`   - Organization Type: ${adminUser.organizationType}`);
    console.log(`   - Organization ID: ${adminUser.organizationId}`);
    console.log(`   - Corporate Sponsor ID: ${adminUser.corporateSponsorId}`);
    
    console.log('\n=== ACCESS CONTROL CHECKS ===');
    
    // Test isAdmin check
    const isAdmin = AccessControlService.isAdmin(adminUser);
    console.log(`✅ isAdmin(adminUser): ${isAdmin}`);
    
    // Test isCorporateSponsorUser check
    const isCorporateSponsorUser = AccessControlService.isCorporateSponsorUser(adminUser);
    console.log(`✅ isCorporateSponsorUser(adminUser): ${isCorporateSponsorUser}`);
    
    // Test getUserEntityFilter
    try {
      const entityFilter = await AccessControlService.getUserEntityFilter(adminUser);
      console.log(`✅ getUserEntityFilter(adminUser): ${JSON.stringify(entityFilter)}`);
      console.log(`   - Filter is empty (admin access): ${Object.keys(entityFilter).length === 0}`);
    } catch (error) {
      console.log(`❌ getUserEntityFilter error: ${error.message}`);
    }
    
    console.log('\n🎉 Admin access control test completed!');
    console.log('The admin user should now be able to access funding-programmes endpoint.');
    
    await mongoose.disconnect();
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

testAdminAccess();
