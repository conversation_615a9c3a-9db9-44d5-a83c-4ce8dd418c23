const express = require('express');
const router = express.Router();
const Application = require('../models/application');
const AuditTrailService = require('../services/audit-trail.service');
const { authenticateToken, addEntityFilter, checkEntityAccess } = require('../middleware/auth');
const { addSMEApplicationFilter } = require('../middleware/enhanced-auth');

// Get application summary by stage
router.get('/summary', authenticateToken, addEntityFilter, checkEntityAccess, async (req, res, next) => {
  try {
    // Get count of applications by main stage
    const { ApplicationMainStage } = require('../models/stage-status-enums');
    const mainStages = Object.values(ApplicationMainStage);
    
    const summary = [];
    
    // Apply entity-based filtering
    const filter = req.entityFilter || {};
    
    // Get all applications to ensure consistent counts
    const allApplications = await Application.find(filter);
    console.log(`Found ${allApplications.length} applications for summary (with entity filter:`, filter, ')');
    
    // Count applications by main stage
    for (const stage of mainStages) {
      const count = allApplications.filter(app => app.currentMainStage === stage).length;
      summary.push({ stage, count });
    }
    
    res.json(summary);
  } catch (err) {
    console.error('Error fetching application summary:', err);
    res.status(500).json({ message: 'Failed to load application summary' });
  }
});

// Get application status counts for dashboard cards
router.get('/status-counts', authenticateToken, addEntityFilter, checkEntityAccess, async (req, res, next) => {
  try {
    // Apply entity-based filtering
    const filter = req.entityFilter || {};
    
    // Get all applications to ensure consistent counts
    const allApplications = await Application.find(filter);
    console.log(`Found ${allApplications.length} applications for status counts (with entity filter:`, filter, ')');
    
    // Initialize counts
    const statusCounts = {
      pending: 0,
      inReview: 0,
      approved: 0,
      rejected: 0,
      withdrawn: 0,
      onHold: 0,
      sentBack: 0,
      total: allApplications.length
    };
    
    // Count applications by status
    allApplications.forEach(app => {
      // Safely get the status, defaulting to empty string if undefined
      const status = (app.status || '').toLowerCase();
      
      // Map the status to the appropriate count
      if (status === 'pending' || status === 'submitted') {
        statusCounts.pending++;
      } else if (status === 'in-review' || status === 'under-review' || status === 'in_review' || status === 'under_review') {
        statusCounts.inReview++;
      } else if (status === 'approved' || status === 'accepted') {
        statusCounts.approved++;
      } else if (status === 'rejected' || status === 'declined') {
        statusCounts.rejected++;
      } else if (status === 'withdrawn-by-applicant' || status === 'withdrawn_by_applicant' || status === 'withdrawn') {
        statusCounts.withdrawn++;
      } else if (status === 'on-hold' || status === 'on_hold') {
        statusCounts.onHold++;
      } else if (status === 'sent-back' || status === 'sent_back') {
        statusCounts.sentBack++;
      } else {
        console.warn(`Unrecognized application status: "${status}" for application ID: ${app.id || 'unknown'}`);
      }
    });
    
    // Log the counts for debugging
    console.log('Status counts calculated:', statusCounts);
    
    // Return the counts
    res.json(statusCounts);
  } catch (err) {
    console.error('Error fetching application status counts:', err);
    res.status(500).json({ message: 'Failed to load application status counts' });
  }
});

// Get filter options endpoint - returns actual values from database
router.get('/filter-options', authenticateToken, addEntityFilter, checkEntityAccess, async (req, res, next) => {
  try {
    console.log('Fetching filter options from actual database data');
    
    // Apply entity-based filtering
    const filter = req.entityFilter || {};
    
    // Get all applications to analyze actual data
    const allApplications = await Application.find(filter)
      .populate('programmeId')
      .populate('corporateSponsorId')
      .lean();
    
    console.log(`Analyzing ${allApplications.length} applications for filter options`);
    
    // Extract unique status values from actual data
    const statusValues = new Set();
    const mainStageValues = new Set();
    const subStageValues = new Set();
    
    allApplications.forEach(app => {
      // Collect actual status values
      if (app.status) {
        statusValues.add(app.status);
      }
      
      // Collect actual main stage values
      if (app.currentMainStage) {
        mainStageValues.add(app.currentMainStage);
      }
      
      // Collect actual sub stage values
      if (app.currentSubStage) {
        subStageValues.add(app.currentSubStage);
      }
    });
    
    // Convert sets to arrays and sort
    const uniqueStatuses = Array.from(statusValues).sort();
    const uniqueMainStages = Array.from(mainStageValues).sort();
    const uniqueSubStages = Array.from(subStageValues).sort();
    
    // Map actual status values to display-friendly values
    const statusOptions = uniqueStatuses.map(status => {
      let displayValue = status;
      
      // Map backend status values to frontend display values
      switch (status.toLowerCase()) {
        case 'pending':
        case 'submitted':
        case 'draft':
          displayValue = 'Pending';
          break;
        case 'in-review':
        case 'under-review':
        case 'under_review':
        case 'in_review':
          displayValue = 'In Review';
          break;
        case 'approved':
        case 'accepted':
          displayValue = 'Approved';
          break;
        case 'rejected':
        case 'declined':
          displayValue = 'Rejected';
          break;
        case 'withdrawn-by-applicant':
        case 'withdrawn_by_applicant':
        case 'withdrawn':
          displayValue = 'Withdrawn';
          break;
        case 'on-hold':
        case 'on_hold':
          displayValue = 'On Hold';
          break;
        case 'sent-back':
        case 'sent_back':
          displayValue = 'Sent Back';
          break;
        default:
          // Capitalize first letter and replace underscores/hyphens with spaces
          displayValue = status.charAt(0).toUpperCase() +
                        status.slice(1).toLowerCase().replace(/[_-]/g, ' ');
      }
      
      return {
        value: status,
        display: displayValue
      };
    });
    
    // Map actual main stage values to display-friendly values
    const mainStageOptions = uniqueMainStages.map(stage => {
      let displayValue = stage;
      
      // Map backend stage values to frontend display values
      switch (stage) {
        case 'ONBOARDING':
          displayValue = 'Onboarding';
          break;
        case 'BUSINESS_CASE_REVIEW':
          displayValue = 'Business Case Review';
          break;
        case 'DUE_DILIGENCE':
          displayValue = 'Due Diligence';
          break;
        case 'ASSESSMENT_REPORT':
          displayValue = 'Assessment Report';
          break;
        case 'APPLICATION_APPROVAL':
          displayValue = 'Application Approval';
          break;
        default:
          // Format as title case with spaces
          displayValue = stage.split('_').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          ).join(' ');
      }
      
      return {
        value: stage,
        display: displayValue
      };
    });
    
    // Map actual sub stage values to display-friendly values
    const subStageOptions = uniqueSubStages.map(subStage => {
      let displayValue = subStage;
      
      // Format as title case with spaces
      displayValue = subStage.split('_').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      ).join(' ');
      
      return {
        value: subStage,
        display: displayValue
      };
    });
    
    // Get active corporate sponsors
    const CorporateSponsor = require('../models/corporate-sponsor');
    const activeSponsors = await CorporateSponsor.find({ status: 'active' })
      .select('_id name')
      .sort({ name: 1 });
    
    const sponsorOptions = activeSponsors.map(sponsor => ({
      value: sponsor._id.toString(),
      display: sponsor.name
    }));
    
    // Get active funding programmes
    const FundingProgramme = require('../models/funding-programme');
    const activePrograms = await FundingProgramme.find({ status: 'active' })
      .select('_id name')
      .sort({ name: 1 });
    
    const programOptions = activePrograms.map(program => ({
      value: program._id.toString(),
      display: program.name
    }));
    
    const filterOptions = {
      statuses: statusOptions,
      mainStages: mainStageOptions,
      subStages: subStageOptions,
      corporateSponsors: sponsorOptions,
      programmes: programOptions,
      metadata: {
        totalApplications: allApplications.length,
        uniqueStatusCount: uniqueStatuses.length,
        uniqueMainStageCount: uniqueMainStages.length,
        uniqueSubStageCount: uniqueSubStages.length,
        activeSponsorCount: activeSponsors.length,
        activeProgramCount: activePrograms.length,
        generatedAt: new Date().toISOString()
      }
    };
    
    console.log('Filter options generated:', {
      statusCount: statusOptions.length,
      mainStageCount: mainStageOptions.length,
      subStageCount: subStageOptions.length,
      sponsorCount: sponsorOptions.length,
      programCount: programOptions.length
    });
    
    res.json(filterOptions);
  } catch (err) {
    console.error('Error fetching filter options:', err);
    res.status(500).json({
      message: 'Failed to load filter options',
      error: err.message
    });
  }
});

// Get all applications with filtering and pagination
router.get('/', authenticateToken, addEntityFilter, addSMEApplicationFilter, checkEntityAccess, async (req, res, next) => {
  try {
    const { page = 1, limit = 25, stage, mainStage, subStage, status, programmeId, corporateSponsorId, searchText, startDate, endDate } = req.query;
    const filter = {};
    
    // Apply entity-based filtering for entity-specific users
    if (req.entityFilter) {
      Object.assign(filter, req.entityFilter);
      console.log('Applied entity filter:', req.entityFilter);
    }
    
    // Fix 1: Enhanced Main Stage Parameter Mapping - Handle both frontend and backend stage values
    const stageParam = mainStage || stage;
    if (stageParam && stageParam !== 'ALL') {
      // Handle comma-separated stages
      let stagesToFilter = [];
      if (stageParam.includes(',')) {
        stagesToFilter = stageParam.split(',').map(s => s.trim());
      } else {
        stagesToFilter = [stageParam];
      }
      
      // Map frontend stage values to backend stage values
      const mappedStages = stagesToFilter.map(stageValue => {
        // If it's already a backend value, use it as-is
        if (['ONBOARDING', 'BUSINESS_CASE_REVIEW', 'DUE_DILIGENCE', 'ASSESSMENT_REPORT', 'APPLICATION_APPROVAL'].includes(stageValue)) {
          return stageValue;
        }
        
        // Map frontend display values to backend enum values
        switch (stageValue) {
          case 'Onboarding':
            return 'ONBOARDING';
          case 'Business Case Review':
            return 'BUSINESS_CASE_REVIEW';
          case 'Due Diligence':
            return 'DUE_DILIGENCE';
          case 'Assessment Report':
            return 'ASSESSMENT_REPORT';
          case 'Application Approval':
            return 'APPLICATION_APPROVAL';
          default:
            // Try to convert to uppercase with underscores
            return stageValue.toUpperCase().replace(/\s+/g, '_');
        }
      });
      
      if (mappedStages.length === 1) {
        filter.currentMainStage = mappedStages[0];
        console.log(`Filtering applications by main stage: ${mappedStages[0]} (from frontend: ${stageParam})`);
      } else {
        filter.currentMainStage = { $in: mappedStages };
        console.log(`Filtering applications by main stages: ${mappedStages.join(', ')} (from frontend: ${stageParam})`);
      }
    }
    
    // Fix 4: Sub Stage Filtering - Simplified null value handling
    if (subStage) {
      // Check if subStage is a comma-separated list
      if (subStage.includes(',')) {
        const subStages = subStage.split(',').map(s => s.trim());
        filter.currentSubStage = { $in: subStages };
        console.log(`Filtering applications by sub stages: ${subStages.join(', ')}`);
      } else {
        filter.currentSubStage = subStage;
        console.log(`Filtering applications by sub stage: ${subStage}`);
      }
    }
    
    // Fix 2: Search Text Filter Implementation
    if (searchText && searchText.trim()) {
      const searchRegex = new RegExp(searchText.trim(), 'i');
      filter.$or = [
        { 'businessInfo.legalName': searchRegex },
        { 'businessInfo.tradingName': searchRegex },
        { 'personalInfo.firstName': searchRegex },
        { 'personalInfo.lastName': searchRegex },
        { 'businessInfo.description': searchRegex },
        { id: searchRegex }
      ];
      console.log(`Filtering applications by search text: "${searchText}"`);
    }
    
    // Fix 3: Date Range Filter Implementation
    if (startDate || endDate) {
      const dateFilter = {};
      
      if (startDate) {
        try {
          const start = new Date(startDate);
          start.setHours(0, 0, 0, 0); // Start of day
          dateFilter.$gte = start;
          console.log(`Filtering applications from date: ${start.toISOString()}`);
        } catch (error) {
          console.warn(`Invalid start date format: ${startDate}`);
        }
      }
      
      if (endDate) {
        try {
          const end = new Date(endDate);
          end.setHours(23, 59, 59, 999); // End of day
          dateFilter.$lte = end;
          console.log(`Filtering applications to date: ${end.toISOString()}`);
        } catch (error) {
          console.warn(`Invalid end date format: ${endDate}`);
        }
      }
      
      if (Object.keys(dateFilter).length > 0) {
        // Filter by submission date, creation date, or last updated date
        filter.$or = filter.$or ? [
          ...filter.$or,
          { submissionDate: dateFilter },
          { createdAt: dateFilter },
          { lastUpdated: dateFilter }
        ] : [
          { submissionDate: dateFilter },
          { createdAt: dateFilter },
          { lastUpdated: dateFilter }
        ];
      }
    }
    
    // Enhanced Status Filtering - Handle actual database status values
    if (status) {
      // Handle both single status and comma-separated statuses
      let statusesToFilter = [];
      if (status.includes(',')) {
        statusesToFilter = status.split(',').map(s => s.trim());
      } else {
        statusesToFilter = [status];
      }
      
      // Map frontend status values to actual database status values
      let allMappedStatuses = [];
      statusesToFilter.forEach(singleStatus => {
        // Check if it's already a database status value (exact match)
        allMappedStatuses.push(singleStatus);
        
        // Also add common variations for better matching
        const upperStatus = singleStatus.toUpperCase();
        const lowerStatus = singleStatus.toLowerCase();
        
        // Add variations based on the status
        if (upperStatus === 'PENDING') {
          allMappedStatuses.push('pending', 'submitted', 'draft');
        } else if (upperStatus === 'IN_REVIEW') {
          allMappedStatuses.push('in-review', 'under-review', 'in_review', 'under_review');
        } else if (upperStatus === 'APPROVED') {
          allMappedStatuses.push('approved', 'accepted');
        } else if (upperStatus === 'REJECTED') {
          allMappedStatuses.push('rejected', 'declined');
        } else if (upperStatus === 'WITHDRAWN' || upperStatus === 'WITHDRAWN_BY_APPLICANT') {
          allMappedStatuses.push('withdrawn', 'withdrawn-by-applicant', 'withdrawn_by_applicant');
        } else if (upperStatus === 'ON_HOLD') {
          allMappedStatuses.push('on-hold', 'on_hold');
        } else if (upperStatus === 'SENT_BACK') {
          allMappedStatuses.push('sent-back', 'sent_back');
        }
      });
      
      // Remove duplicates
      allMappedStatuses = [...new Set(allMappedStatuses)];
      
      filter.status = { $in: allMappedStatuses };
      console.log(`Status filter: Frontend sent "${status}", mapped to database values:`, allMappedStatuses);
    }
    
    if (programmeId) filter.programmeId = programmeId;
    
    // Fix 4: Enhanced Corporate Sponsor ObjectId Conversion
    if (corporateSponsorId) {
      try {
        const mongoose = require('mongoose');
        
        // Handle comma-separated sponsor IDs
        if (corporateSponsorId.includes(',')) {
          const sponsorIds = corporateSponsorId.split(',').map(id => id.trim());
          const convertedIds = sponsorIds.map(id => {
            if (mongoose.Types.ObjectId.isValid(id)) {
              return new mongoose.Types.ObjectId(id);
            }
            return id; // Keep as string if not valid ObjectId
          });
          filter.corporateSponsorId = { $in: convertedIds };
          console.log(`Corporate sponsor filter: Converted multiple IDs:`, convertedIds);
        } else {
          // Single sponsor ID
          if (mongoose.Types.ObjectId.isValid(corporateSponsorId)) {
            filter.corporateSponsorId = new mongoose.Types.ObjectId(corporateSponsorId);
            console.log(`Corporate sponsor filter: Converted string "${corporateSponsorId}" to ObjectId`);
          } else {
            // If not a valid ObjectId, use as string (fallback)
            filter.corporateSponsorId = corporateSponsorId;
            console.log(`Corporate sponsor filter: Using string "${corporateSponsorId}" (not valid ObjectId)`);
          }
        }
      } catch (error) {
        // If conversion fails, use as string
        filter.corporateSponsorId = corporateSponsorId;
        console.log(`Corporate sponsor filter: ObjectId conversion failed, using string "${corporateSponsorId}"`);
      }
    }

    // Add Debug Logging for Filter Parameters
    console.log('Application filter with critical fixes applied:', filter);
    
    // Log actual database values vs filter parameters for debugging
    if (status || stageParam || subStage || searchText || startDate || endDate) {
      console.log('=== FILTER DEBUG INFO ===');
      if (status) {
        console.log(`Frontend status parameter: "${status}"`);
        console.log(`Database status filter:`, filter.status);
      }
      if (stageParam) {
        console.log(`Frontend stage parameter: "${stageParam}" (from ${mainStage ? 'mainStage' : 'stage'})`);
        console.log(`Database stage filter:`, filter.currentMainStage);
      }
      if (subStage) {
        console.log(`Frontend subStage parameter: "${subStage}"`);
        console.log(`Database subStage filter:`, filter.currentSubStage);
      }
      if (searchText) {
        console.log(`Frontend searchText parameter: "${searchText}"`);
        console.log(`Database search filter:`, filter.$or);
      }
      if (startDate || endDate) {
        console.log(`Frontend date range: ${startDate} to ${endDate}`);
      }
      console.log('========================');
    }
    
    const applications = await Application.find(filter)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('programmeId')  // Populate program data
      .populate('corporateSponsorId')  // Populate sponsor data
      .populate('businessInfo personalInfo stageHierarchy')
      .lean();

    console.log(`Found ${applications.length} applications matching filter`);
    
    const count = await Application.countDocuments(filter);

    // Log the first application to see if it has financialInfo.requestedAmount
    if (applications.length > 0) {
      console.log('First application data:', {
        id: applications[0].id,
        fundingAmount: applications[0].fundingAmount,
        financialInfo: applications[0].financialInfo,
        requestedAmount: applications[0].requestedAmount
      });
    }

    res.json({
      total: count,
      page: Number(page),
      totalPages: Math.ceil(count / limit),
      data: applications.map(app => ({
        ...app,
        currentMainStage: app.currentMainStage,
        currentSubStage: app.currentSubStage,
        currentStageStatus: app.currentStageStatus,
        businessName: app.businessInfo?.legalName,
        applicantName: `${app.personalInfo?.firstName} ${app.personalInfo?.lastName}`,
        programmeId: app.programmeId,
        corporateSponsorId: app.corporateSponsorId,
        requestedAmount: app.financialInfo?.requestedAmount || app.requestedAmount || app.financialInfo?.fundingAmount || app.fundingAmount
      }))
    });
  } catch (err) {
    console.error('Failed to fetch applications:', err);
    res.status(500).json({ message: 'Failed to load applications' });
  }
});

// Get application by ID
router.get('/:id', async (req, res, next) => {
  try {
    const application = await Application.findOne({ id: req.params.id })
      .populate('programmeId')  // Populate program data
      .populate('corporateSponsorId')  // Populate sponsor data
      .lean();
    
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    res.json(application);
  } catch (err) {
    next(err);
  }
});

// Create new application
router.post('/', async (req, res, next) => {
  try {
    // Ensure required fields are provided
    if (!req.body.programmeId || !req.body.corporateSponsorId) {
      return res.status(400).json({ 
        message: 'Programme ID and Corporate Sponsor ID are required',
        success: false 
      });
    }
    
    // CRITICAL: Ensure SME Entity ID is provided and exists
    if (!req.body.smeEntityId) {
      return res.status(400).json({ 
        message: 'SME Entity ID is required. An SME must be created before creating an application.',
        success: false 
      });
    }
    
    // Validate that the SME entity exists and is active
    const SMEEntity = require('../models/sme-entity');
    const smeEntity = await SMEEntity.findById(req.body.smeEntityId);
    
    if (!smeEntity) {
      return res.status(404).json({ 
        message: 'SME Entity not found. Please ensure the SME entity exists before creating an application.',
        success: false 
      });
    }
    
    // Check if SME entity is in a valid status for application creation
    if (smeEntity.status === 'inactive' || smeEntity.status === 'suspended') {
      return res.status(400).json({ 
        message: `Cannot create application for SME entity with status: ${smeEntity.status}. SME entity must be active.`,
        success: false 
      });
    }
    
    // Validate that the SME entity is registered for the specified programme
    const isRegisteredForProgramme = smeEntity.programmeRegistrations.some(
      reg => reg.programmeId.toString() === req.body.programmeId.toString() && 
             reg.status === 'active' && 
             reg.eligibilityStatus === 'eligible'
    );
    
    if (!isRegisteredForProgramme) {
      return res.status(400).json({ 
        message: 'SME Entity is not registered or eligible for the specified funding programme. Please ensure the SME is registered and eligible for the programme before creating an application.',
        success: false 
      });
    }
    
    // Generate a unique ID if not provided
    if (!req.body.id) {
      const count = await Application.countDocuments();
      const nextNumber = (count + 1).toString().padStart(3, '0');
      req.body.id = `APP-2025-${nextNumber}`;
    }
    
    // Create the application with SME entity reference
    const savedApplication = await Application.create(req.body);
    
    // Populate the SME entity details in the response
    await savedApplication.populate('smeEntityId');
    
    console.log(`Application ${savedApplication.id} created successfully for SME Entity ${smeEntity.id} (${smeEntity.businessRegistration.legalName})`);
    
    res.status(201).json({
      ...savedApplication.toObject(),
      success: true,
      message: `Application created successfully for SME: ${smeEntity.businessRegistration.legalName}`
    });
  } catch (err) {
    console.error('Error creating application:', err);
    if (err.name === 'ValidationError') {
      return res.status(400).json({
        message: 'Validation error',
        errors: err.errors,
        success: false
      });
    }
    next(err);
  }
});

// Update application

router.put('/:id', async (req, res, next) => {
  try {
    const app = await Application.findOne({ id: req.params.id });

    if (!app) {
      return res.status(404).json({ message: 'Application not found' });
    }

    // Merge updated fields
 
    app.set(req.body);

    // Save the updated application
    await app.save();

    // Populate after save
    await app.populate(['programmeId', 'corporateSponsorId']);

    res.json(app);
  } catch (err) {
    next(err);
  }
});

// router.put('/:id', async (req, res, next) => {
//   try {
//     // Get the current application state for audit trail
//     const currentApplication = await Application.findOne({ id: req.params.id }).lean();
    
//     if (!currentApplication) {
//       return res.status(404).json({ message: 'Application not found' });
//     }

//     const updatedApplication = await Application.findOneAndUpdate(
//       { id: req.params.id },
//       req.body,
//       { new: true, runValidators: true }
//     )
//     .populate('programmeId')  // Populate program data
//     .populate('corporateSponsorId')  // Populate sponsor data
//     .lean();
    
//     // Log audit trail if stage-related fields changed
//     const stageFieldsChanged =
//       req.body.currentMainStage !== undefined ||
//       req.body.currentSubStage !== undefined ||
//       req.body.currentStageStatus !== undefined ||
//       req.body.status !== undefined;

//     if (stageFieldsChanged) {
//       try {
//         await AuditTrailService.logStageTransition({
//           applicationId: req.params.id,
//           applicationObjectId: updatedApplication._id,
//           from: {
//             mainStage: currentApplication.currentMainStage,
//             subStage: currentApplication.currentSubStage,
//             stageStatus: currentApplication.currentStageStatus,
//             applicationStatus: currentApplication.status
//           },
//           to: {
//             mainStage: updatedApplication.currentMainStage,
//             subStage: updatedApplication.currentSubStage,
//             stageStatus: updatedApplication.currentStageStatus,
//             applicationStatus: updatedApplication.status
//           },
//           context: {
//             userId: req.user?.id || 'unknown',
//             userName: req.user?.name || 'Unknown User',
//             reason: 'Application update',
//             notes: `Application updated via PUT /applications/${req.params.id}`,
//             isSystemGenerated: false
//           }
//         });
//       } catch (auditError) {
//         console.error('Failed to log audit trail:', auditError);
//         // Don't fail the request if audit logging fails
//       }
//     }
    
//     res.json(updatedApplication);
//   } catch (err) {
//     next(err);
//   }
// });

// Delete application
router.delete('/:id', async (req, res, next) => {
  try {
    const result = await Application.findOneAndDelete({ id: req.params.id });
    
    if (!result) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    res.json({ success: true });
  } catch (err) {
    next(err);
  }
});

// Get site visits for an application
router.get('/:applicationId/site-visits', async (req, res, next) => {
  try {
    const SiteVisit = require('../models/site-visit');
    const siteVisits = await SiteVisit.findByApplicationId(req.params.applicationId)
      .populate('programmeId')  // Populate program data
      .populate('corporateSponsorId')  // Populate sponsor data
      .lean();
    res.json(siteVisits);
  } catch (err) {
    console.error('Error fetching site visits for application:', err);
    res.status(500).json({ message: 'Failed to load site visits for application' });
  }
});

// Update application stage with SME data
router.put('/:id/stage', async (req, res, next) => {
  try {
    const { mainStage, subStage, status, notes, interviewData } = req.body;
    
    if (!mainStage || !subStage) {
      return res.status(400).json({ message: 'Main stage and sub-stage are required' });
    }
    
    // Get current application state for audit trail
    const currentApplication = await Application.findById(req.params.id).lean();
    if (!currentApplication) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    // Extract status from notes if it contains a status marker
    let stageStatus = 'COMPLETED';
    const statusMatch = notes && notes.match(/\[Status: (.*?)\]/);
    if (statusMatch && statusMatch[1]) {
      stageStatus = statusMatch[1].toUpperCase().replace(/ /g, '_');
    }

    const updateData = {
      currentMainStage: mainStage,
      currentSubStage: subStage,
      currentStageStatus: stageStatus,
      status: status || 'in-review', // Add the application status
      lastUpdated: new Date()
    };

    // If updating to SME interview stage, set initial interview data
    if (subStage === 'SME_INTERVIEW') {
      updateData.smeInterview = {
        status: 'SCHEDULED',
        scheduledDate: interviewData?.scheduledDate,
        participants: interviewData?.participants || []
      };
    }

    const updatedApplication = await Application.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )
    .populate('programmeId')  // Populate program data
    .populate('corporateSponsorId')  // Populate sponsor data
    .populate('businessInfo personalInfo stageHierarchy');
    
    if (!updatedApplication) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    // Log audit trail for stage transition
    try {
      await AuditTrailService.logStageTransition({
        applicationId: currentApplication.id,
        applicationObjectId: updatedApplication._id,
        from: {
          mainStage: currentApplication.currentMainStage,
          subStage: currentApplication.currentSubStage,
          stageStatus: currentApplication.currentStageStatus,
          applicationStatus: currentApplication.status
        },
        to: {
          mainStage: updatedApplication.currentMainStage,
          subStage: updatedApplication.currentSubStage,
          stageStatus: updatedApplication.currentStageStatus,
          applicationStatus: updatedApplication.status
        },
        context: {
          userId: req.user?.id || 'unknown',
          userName: req.user?.name || 'Unknown User',
          reason: 'Stage update with SME data',
          notes: notes || `Stage updated to ${mainStage}/${subStage}`,
          isSystemGenerated: false
        }
      });
    } catch (auditError) {
      console.error('Failed to log audit trail:', auditError);
      // Don't fail the request if audit logging fails
    }
    
    res.json({
      success: true,
      application: {
        ...updatedApplication.toObject(),
        businessName: updatedApplication.businessInfo?.legalName,
        applicantName: `${updatedApplication.personalInfo?.firstName} ${updatedApplication.personalInfo?.lastName}`
      }
    });
  } catch (err) {
    console.error('Stage update failed:', err);
    res.status(500).json({ message: 'Stage update failed', error: err.message });
  }
});

// Update application stage with three-level hierarchy
router.put('/:id/stage-hierarchy', async (req, res, next) => {
  try {
    const { mainStage, subStage, status, notes } = req.body;
    
    if (!mainStage || !subStage || !status) {
      return res.status(400).json({
        message: 'Main stage, sub-stage, and status are required',
        success: false
      });
    }
    
    // Get current application state for audit trail
    const currentApplication = await Application.findOne({ id: req.params.id }).lean();
    if (!currentApplication) {
      return res.status(404).json({
        message: 'Application not found',
        success: false
      });
    }
    
    // Get the main stage from the sub-stage if not provided
    const { SubStageToMainStageMap } = require('../models/stage-status-enums');
    const derivedMainStage = mainStage || SubStageToMainStageMap[subStage] || 'ONBOARDING';
    
    // Map the status to a backend status
    let backendStatus;
    switch(status) {
      case 'Complete':
      case 'COMPLETE':
        backendStatus = 'COMPLETED';
        break;
      case 'In Progress':
      case 'IN_PROGRESS':
        backendStatus = 'IN_PROGRESS';
        break;
      case 'Not Started':
      case 'NOT_STARTED':
        backendStatus = 'PENDING';
        break;
      case 'Approved':
      case 'APPROVED':
        backendStatus = 'COMPLETED';
        break;
      case 'Rejected':
      case 'REJECTED':
        backendStatus = 'REJECTED';
        break;
      default:
        backendStatus = 'IN_PROGRESS';
    }
    
    // Format the notes to include the hierarchy information
    const formattedNotes = `${notes || ''} [Main Stage: ${mainStage}, Sub-Stage: ${subStage}, Status: ${status}]`;
    
    // Determine appropriate application status based on the stage
    let applicationStatus = 'in-review'; // Default status
    
    // Map main stages to appropriate application statuses
    if (derivedMainStage === 'ONBOARDING') {
      applicationStatus = 'pending';
    } else if (derivedMainStage === 'BUSINESS_CASE_REVIEW' || derivedMainStage === 'DUE_DILIGENCE') {
      applicationStatus = 'in-review';
    } else if (derivedMainStage === 'ASSESSMENT_REPORT') {
      // Keep as in-review unless explicitly approved or rejected
      if (backendStatus === 'REJECTED') {
        applicationStatus = 'rejected';
      }
    } else if (derivedMainStage === 'APPLICATION_APPROVAL') {
      // For approval stage, use the backend status to determine application status
      if (backendStatus === 'COMPLETED') {
        applicationStatus = 'approved';
      } else if (backendStatus === 'REJECTED') {
        applicationStatus = 'rejected';
      } else if (req.body.status === 'rejected') {
        // If the client explicitly sets the status to 'rejected', honor that
        applicationStatus = 'rejected';
        console.log(`Honoring explicit 'rejected' status from client for application ${req.params.id}`);
      }
    }
    
    console.log(`Setting application status to ${applicationStatus} for stage ${derivedMainStage} with status ${backendStatus}`);
    
    // Update the application using MongoDB
    const updatedApplication = await Application.findOneAndUpdate(
      { id: req.params.id },
      {
        currentMainStage: derivedMainStage,
        currentSubStage: subStage,
        currentStageStatus: backendStatus,
        status: applicationStatus, // Set the application status based on the stage
        lastUpdated: new Date()
      },
      { new: true }
    )
    .populate('programmeId')  // Populate program data
    .populate('corporateSponsorId');  // Populate sponsor data
    
    if (!updatedApplication) {
      return res.status(404).json({
        message: 'Application not found',
        success: false
      });
    }
    
    // Log audit trail for stage hierarchy transition
    try {
      await AuditTrailService.logStageTransition({
        applicationId: req.params.id,
        applicationObjectId: updatedApplication._id,
        from: {
          mainStage: currentApplication.currentMainStage,
          subStage: currentApplication.currentSubStage,
          stageStatus: currentApplication.currentStageStatus,
          applicationStatus: currentApplication.status
        },
        to: {
          mainStage: updatedApplication.currentMainStage,
          subStage: updatedApplication.currentSubStage,
          stageStatus: updatedApplication.currentStageStatus,
          applicationStatus: updatedApplication.status
        },
        context: {
          userId: req.user?.id || 'unknown',
          userName: req.user?.name || 'Unknown User',
          reason: 'Stage hierarchy update',
          notes: formattedNotes,
          isSystemGenerated: false
        }
      });
    } catch (auditError) {
      console.error('Failed to log audit trail:', auditError);
      // Don't fail the request if audit logging fails
    }
    
    res.json({
      success: true,
      message: 'Application stage updated successfully',
      application: {
        ...updatedApplication,
        businessName: updatedApplication.businessInfo?.legalName,
        applicantName: `${updatedApplication.personalInfo?.firstName || ''} ${updatedApplication.personalInfo?.lastName || ''}`
      }
    });
  } catch (err) {
    console.error('Hierarchy stage update failed:', err);
    res.status(500).json({
      message: 'Stage update failed',
      error: err.message,
      success: false
    });
  }
});

// Get approval workflow for an application
router.get('/:id/approval-workflow', async (req, res, next) => {
  try {
    const application = await Application.findOne({ id: req.params.id })
      .populate('programmeId')  // Populate program data
      .populate('corporateSponsorId')  // Populate sponsor data
      .lean();
    
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    if (!application.approvalWorkflow) {
      return res.status(404).json({ message: 'Approval workflow not found for this application' });
    }
    
    res.json(application.approvalWorkflow);
  } catch (err) {
    console.error('Error fetching approval workflow:', err);
    res.status(500).json({ message: 'Failed to load approval workflow' });
  }
});

// Update approval workflow for an application
router.put('/:id/approval-workflow', async (req, res, next) => {
  try {
    const { currentStep, step, status, assignedTo, notes, attachments } = req.body;
    
    const application = await Application.findOne({ id: req.params.id })
      .populate('programmeId')  // Populate program data
      .populate('corporateSponsorId')  // Populate sponsor data
      .lean();
    
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    // Initialize approval workflow if it doesn't exist
    if (!application.approvalWorkflow) {
      application.approvalWorkflow = {
        currentStep: 'ANALYST_REPORT',
        steps: [],
        committeeMeetings: []
      };
    }
    
    // Update current step if provided
    if (currentStep) {
      application.approvalWorkflow.currentStep = currentStep;
    }
    
    // Add a new step if step details are provided
    if (step) {
      const newStep = {
        step,
        status: status || 'pending',
        assignedTo,
        startDate: new Date(),
        notes,
        attachments: attachments || []
      };
      
      // Check if step already exists
      const existingStepIndex = application.approvalWorkflow.steps.findIndex(s => s.step === step);
      
      if (existingStepIndex !== -1) {
        // Update existing step
        application.approvalWorkflow.steps[existingStepIndex] = {
          ...application.approvalWorkflow.steps[existingStepIndex],
          ...newStep
        };
        
        // Set completion date if status is completed
        if (status === 'completed') {
          application.approvalWorkflow.steps[existingStepIndex].completionDate = new Date();
        }
      } else {
        // Add new step
        application.approvalWorkflow.steps.push(newStep);
      }
    }
    
    await application.save();
    
    res.json({
      success: true,
      message: 'Approval workflow updated successfully',
      approvalWorkflow: application.approvalWorkflow
    });
  } catch (err) {
    console.error('Error updating approval workflow:', err);
    res.status(500).json({ message: 'Failed to update approval workflow' });
  }
});

// GET /api/v1/applications/:id/scores
router.get('/:id/scores', async (req, res, next) => {
  try {
    const application = await Application.findOne({ id: req.params.id })
      .populate('programmeId')
      .populate('corporateSponsorId')
      .lean();
    
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    // Generate realistic scoring data based on application details
    const overallScore = Math.floor(Math.random() * 30) + 70; // Score between 70-100
    
    const scoreData = {
      applicationId: req.params.id,
      overallScore: overallScore,
      mainStageScores: [
        {
          mainStage: 'Onboarding',
          score: Math.floor(Math.random() * 20) + 80, // 80-100
          subStageScores: [
            {
              subStage: 'Beneficiary Registration',
              score: Math.floor(Math.random() * 15) + 85, // 85-100
              categoryScores: [
                {
                  category: 'Identity Verification',
                  score: Math.floor(Math.random() * 10) + 90, // 90-100
                  items: [
                    {
                      id: '1.1',
                      category: 'Identity Verification',
                      text: 'Valid ID document provided',
                      description: 'Check if the applicant has provided a valid ID document',
                      weight: 5,
                      required: true,
                      flags: ['critical'],
                      score: 5,
                      comment: 'All documents provided and verified'
                    },
                    {
                      id: '1.2',
                      category: 'Identity Verification',
                      text: 'Proof of address provided',
                      description: 'Check if the applicant has provided proof of address',
                      weight: 3,
                      required: true,
                      score: 4,
                      comment: 'Document provided and verified'
                    }
                  ]
                },
                {
                  category: 'Eligibility Criteria',
                  score: Math.floor(Math.random() * 15) + 80, // 80-95
                  items: [
                    {
                      id: '2.1',
                      category: 'Eligibility Criteria',
                      text: 'Meets size requirements',
                      description: 'Check if the business meets the size requirements for funding',
                      weight: 4,
                      required: true,
                      flags: ['critical'],
                      score: 5,
                      comment: 'Well within size requirements'
                    },
                    {
                      id: '2.2',
                      category: 'Eligibility Criteria',
                      text: 'Operates in eligible sector',
                      description: 'Check if the business operates in an eligible sector',
                      weight: 4,
                      required: true,
                      flags: ['critical'],
                      score: 4,
                      comment: 'Operates in priority sector'
                    }
                  ]
                }
              ]
            },
            {
              subStage: 'Pre-screening',
              score: Math.floor(Math.random() * 20) + 75, // 75-95
              categoryScores: [
                {
                  category: 'Basic Requirements',
                  score: Math.floor(Math.random() * 15) + 80,
                  items: []
                }
              ]
            }
          ]
        },
        {
          mainStage: 'Business Case Review',
          score: Math.floor(Math.random() * 25) + 65, // 65-90
          subStageScores: [
            {
              subStage: 'Document Collection',
              score: Math.floor(Math.random() * 20) + 70, // 70-90
              categoryScores: [
                {
                  category: 'Business Documents',
                  score: Math.floor(Math.random() * 15) + 75,
                  items: []
                },
                {
                  category: 'Financial Documents',
                  score: Math.floor(Math.random() * 20) + 70,
                  items: []
                }
              ]
            },
            {
              subStage: 'Desktop Analysis',
              score: Math.floor(Math.random() * 25) + 60, // 60-85
              categoryScores: [
                {
                  category: 'Financial Analysis',
                  score: Math.floor(Math.random() * 20) + 65,
                  items: []
                },
                {
                  category: 'Business Viability',
                  score: Math.floor(Math.random() * 25) + 60,
                  items: []
                }
              ]
            }
          ]
        },
        {
          mainStage: 'Due Diligence',
          score: Math.floor(Math.random() * 30) + 60, // 60-90
          subStageScores: [
            {
              subStage: 'SME Interview',
              score: Math.floor(Math.random() * 25) + 65, // 65-90
              categoryScores: [
                {
                  category: 'Management Capability',
                  score: Math.floor(Math.random() * 20) + 70,
                  items: []
                },
                {
                  category: 'Business Understanding',
                  score: Math.floor(Math.random() * 25) + 65,
                  items: []
                }
              ]
            },
            {
              subStage: 'Site Visit',
              score: Math.floor(Math.random() * 20) + 70, // 70-90
              categoryScores: [
                {
                  category: 'Physical Infrastructure',
                  score: Math.floor(Math.random() * 15) + 75,
                  items: []
                },
                {
                  category: 'Operational Readiness',
                  score: Math.floor(Math.random() * 20) + 70,
                  items: []
                }
              ]
            }
          ]
        }
      ],
      criticalIssues: overallScore < 75 ? [
        'Financial data inconsistency in Desktop Analysis',
        'Missing required documentation in Document Collection'
      ] : overallScore < 85 ? [
        'Minor documentation gaps identified'
      ] : [],
      anomalies: overallScore < 80 ? [
        'Unusually high revenue projection compared to industry average',
        'Significant discrepancy between reported assets and liabilities'
      ] : [],
      lastCalculated: new Date()
    };
    
    res.json(scoreData);
  } catch (err) {
    console.error('Error fetching application scores:', err);
    res.status(500).json({ message: 'Failed to load application scores' });
  }
});

// GET /api/v1/applications/:id/historical-comparison
router.get('/:id/historical-comparison', async (req, res, next) => {
  try {
    const application = await Application.findOne({ id: req.params.id })
      .populate('programmeId')
      .populate('corporateSponsorId')
      .lean();
    
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    // Generate realistic historical comparison data
    const applicationScore = Math.floor(Math.random() * 30) + 70; // 70-100
    const averageScore = Math.floor(Math.random() * 20) + 65; // 65-85
    const similarApplications = Math.floor(Math.random() * 50) + 20; // 20-70
    
    const historicalData = {
      applicationId: req.params.id,
      percentile: Math.floor((applicationScore / 100) * 100), // Convert score to percentile
      similarApplications: similarApplications,
      averageScore: averageScore,
      applicationScore: applicationScore,
      timeframe: 'Last 12 months',
      comparisonCriteria: {
        industry: application.businessInfo?.industry || 'General',
        fundingRange: application.financialInfo?.requestedAmount || application.fundingAmount || 'Unknown',
        region: application.businessInfo?.address?.province || 'National'
      },
      categoryComparisons: [
        {
          category: 'Financial Viability',
          applicationScore: Math.floor(Math.random() * 25) + 70,
          averageScore: Math.floor(Math.random() * 20) + 65,
          percentile: Math.floor(Math.random() * 40) + 60
        },
        {
          category: 'Business Plan Quality',
          applicationScore: Math.floor(Math.random() * 30) + 65,
          averageScore: Math.floor(Math.random() * 25) + 60,
          percentile: Math.floor(Math.random() * 35) + 55
        },
        {
          category: 'Market Analysis',
          applicationScore: Math.floor(Math.random() * 25) + 60,
          averageScore: Math.floor(Math.random() * 20) + 65,
          percentile: Math.floor(Math.random() * 30) + 50
        },
        {
          category: 'Management Capability',
          applicationScore: Math.floor(Math.random() * 20) + 75,
          averageScore: Math.floor(Math.random() * 15) + 70,
          percentile: Math.floor(Math.random() * 25) + 65
        },
        {
          category: 'Risk Assessment',
          applicationScore: Math.floor(Math.random() * 30) + 60,
          averageScore: Math.floor(Math.random() * 25) + 65,
          percentile: Math.floor(Math.random() * 40) + 45
        }
      ],
      trends: {
        improvementAreas: [
          'Market analysis depth',
          'Financial projections accuracy',
          'Risk mitigation strategies'
        ],
        strongAreas: [
          'Business registration compliance',
          'Management experience',
          'Industry knowledge'
        ]
      },
      generatedDate: new Date()
    };
    
    res.json(historicalData);
  } catch (err) {
    console.error('Error fetching historical comparison:', err);
    res.status(500).json({ message: 'Failed to load historical comparison data' });
  }
});

// GET /api/v1/applications/:id/benchmark-comparison
router.get('/:id/benchmark-comparison', async (req, res, next) => {
  try {
    const application = await Application.findOne({ id: req.params.id })
      .populate('programmeId')
      .populate('corporateSponsorId')
      .lean();
    
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    // Generate realistic benchmark comparison data
    const industry = application.businessInfo?.industry || 'Technology';
    const subIndustry = getSubIndustry(industry);
    
    const benchmarkData = {
      applicationId: req.params.id,
      industry: industry,
      subIndustry: subIndustry,
      benchmarkSource: 'Industry Standards Database',
      lastUpdated: new Date(),
      sampleSize: Math.floor(Math.random() * 500) + 100, // 100-600 applications
      benchmarks: [
        {
          category: 'Financial Viability',
          applicationScore: Math.floor(Math.random() * 25) + 70,
          benchmarkScore: Math.floor(Math.random() * 15) + 75,
          difference: 0, // Will be calculated
          industryPercentile: Math.floor(Math.random() * 40) + 60,
          interpretation: ''
        },
        {
          category: 'Business Plan Quality',
          applicationScore: Math.floor(Math.random() * 30) + 65,
          benchmarkScore: Math.floor(Math.random() * 20) + 70,
          difference: 0,
          industryPercentile: Math.floor(Math.random() * 35) + 55,
          interpretation: ''
        },
        {
          category: 'Market Analysis',
          applicationScore: Math.floor(Math.random() * 25) + 60,
          benchmarkScore: Math.floor(Math.random() * 20) + 68,
          difference: 0,
          industryPercentile: Math.floor(Math.random() * 30) + 50,
          interpretation: ''
        },
        {
          category: 'Management Capability',
          applicationScore: Math.floor(Math.random() * 20) + 75,
          benchmarkScore: Math.floor(Math.random() * 15) + 72,
          difference: 0,
          industryPercentile: Math.floor(Math.random() * 25) + 65,
          interpretation: ''
        },
        {
          category: 'Innovation Potential',
          applicationScore: Math.floor(Math.random() * 30) + 60,
          benchmarkScore: Math.floor(Math.random() * 25) + 65,
          difference: 0,
          industryPercentile: Math.floor(Math.random() * 40) + 45,
          interpretation: ''
        }
      ],
      overallComparison: {
        applicationOverallScore: Math.floor(Math.random() * 25) + 70,
        industryBenchmark: Math.floor(Math.random() * 15) + 72,
        percentileRanking: Math.floor(Math.random() * 40) + 55,
        competitivePosition: ''
      },
      recommendations: [],
      riskFactors: []
    };
    
    // Calculate differences and interpretations
    benchmarkData.benchmarks.forEach(benchmark => {
      benchmark.difference = benchmark.applicationScore - benchmark.benchmarkScore;
      
      if (benchmark.difference >= 10) {
        benchmark.interpretation = 'Significantly above industry benchmark';
      } else if (benchmark.difference >= 5) {
        benchmark.interpretation = 'Above industry benchmark';
      } else if (benchmark.difference >= -5) {
        benchmark.interpretation = 'At industry benchmark level';
      } else if (benchmark.difference >= -10) {
        benchmark.interpretation = 'Below industry benchmark';
      } else {
        benchmark.interpretation = 'Significantly below industry benchmark';
      }
    });
    
    // Calculate overall comparison
    benchmarkData.overallComparison.difference =
      benchmarkData.overallComparison.applicationOverallScore -
      benchmarkData.overallComparison.industryBenchmark;
    
    if (benchmarkData.overallComparison.percentileRanking >= 80) {
      benchmarkData.overallComparison.competitivePosition = 'Top performer in industry';
    } else if (benchmarkData.overallComparison.percentileRanking >= 60) {
      benchmarkData.overallComparison.competitivePosition = 'Above average performer';
    } else if (benchmarkData.overallComparison.percentileRanking >= 40) {
      benchmarkData.overallComparison.competitivePosition = 'Average performer';
    } else {
      benchmarkData.overallComparison.competitivePosition = 'Below average performer';
    }
    
    // Generate recommendations based on performance
    benchmarkData.recommendations = generateRecommendations(benchmarkData.benchmarks);
    benchmarkData.riskFactors = generateRiskFactors(benchmarkData.benchmarks);
    
    res.json(benchmarkData);
  } catch (err) {
    console.error('Error fetching benchmark comparison:', err);
    res.status(500).json({ message: 'Failed to load benchmark comparison data' });
  }
});

// Helper function to determine sub-industry
function getSubIndustry(industry) {
  const subIndustries = {
    'Technology': ['Software Development', 'Hardware Manufacturing', 'IT Services', 'Telecommunications'],
    'Manufacturing': ['Food Processing', 'Textiles', 'Automotive Parts', 'Electronics'],
    'Services': ['Professional Services', 'Financial Services', 'Healthcare Services', 'Education'],
    'Retail': ['E-commerce', 'Fashion Retail', 'Food & Beverage', 'Electronics Retail'],
    'Agriculture': ['Crop Production', 'Livestock', 'Agro-processing', 'Aquaculture'],
    'Construction': ['Residential Construction', 'Commercial Construction', 'Infrastructure', 'Renovation']
  };
  
  const options = subIndustries[industry] || ['General Business'];
  return options[Math.floor(Math.random() * options.length)];
}

// Helper function to generate recommendations
function generateRecommendations(benchmarks) {
  const recommendations = [];
  
  benchmarks.forEach(benchmark => {
    if (benchmark.difference < -5) {
      switch (benchmark.category) {
        case 'Financial Viability':
          recommendations.push('Strengthen financial planning and cash flow management');
          break;
        case 'Business Plan Quality':
          recommendations.push('Enhance business plan with more detailed market research and financial projections');
          break;
        case 'Market Analysis':
          recommendations.push('Conduct more comprehensive market analysis and competitive research');
          break;
        case 'Management Capability':
          recommendations.push('Consider management training or advisory support');
          break;
        case 'Innovation Potential':
          recommendations.push('Explore opportunities for innovation and differentiation');
          break;
      }
    }
  });
  
  if (recommendations.length === 0) {
    recommendations.push('Continue maintaining current performance standards');
    recommendations.push('Consider opportunities for further improvement and growth');
  }
  
  return recommendations;
}

// Helper function to generate risk factors
function generateRiskFactors(benchmarks) {
  const riskFactors = [];
  
  benchmarks.forEach(benchmark => {
    if (benchmark.difference < -10) {
      switch (benchmark.category) {
        case 'Financial Viability':
          riskFactors.push('High financial risk due to weak financial position');
          break;
        case 'Business Plan Quality':
          riskFactors.push('Business plan lacks sufficient detail and clarity');
          break;
        case 'Market Analysis':
          riskFactors.push('Limited understanding of market dynamics and competition');
          break;
        case 'Management Capability':
          riskFactors.push('Management team may lack necessary experience or skills');
          break;
        case 'Innovation Potential':
          riskFactors.push('Limited innovation potential may affect long-term competitiveness');
          break;
      }
    }
  });
  
  return riskFactors;
}

module.exports = router;
