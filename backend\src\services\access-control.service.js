const { normalizeObjectId, normalizeObjectIdArray } = require('../utils/object-id-utils');

/**
 * Centralized Access Control Service
 * Provides unified access control logic for all entity types
 */
class AccessControlService {
  /**
   * Generate entity filter for user based on their roles and organization
   * @param {Object} user - User object with roles and organization info
   * @returns {Object} Entity filter object for database queries
   */
  static async getUserEntityFilter(user) {
    const filter = {};
    const userRoles = user.roles || [];
    const organizationType = user.organizationType;
    
    console.log('AccessControlService: Generating entity filter for user:', {
      id: user.id,
      roles: userRoles,
      organizationType,
      organizationId: user.organizationId,
      corporateSponsorId: user.corporateSponsorId,
      smeEntityId: user.smeEntityId
    });
    
    // Admin users - no filtering (can see all data)
    if (this.isAdmin(user)) {
      console.log('AccessControlService: Admin user - no filtering applied');
      return {};
    }
    
    // Corporate sponsor filtering
    if (this.isCorporateSponsorUser(user)) {
      const sponsorId = user.corporateSponsorId || user.organizationId;
      if (!sponsorId) {
        throw new Error('Corporate sponsor user missing organization ID');
      }
      
      filter.corporateSponsorId = normalizeObjectId(sponsorId);
      console.log(`AccessControlService: Applied corporate sponsor filter: ${sponsorId}`);
    }
    
    // Programme filtering
    if (this.isProgrammeUser(user)) {
      const programmeIds = this.getUserProgrammeIds(user);
      if (programmeIds.length === 0) {
        throw new Error('Programme user missing programme assignments');
      }
      
      filter.programmeId = { $in: normalizeObjectIdArray(programmeIds) };
      console.log(`AccessControlService: Applied programme filter: ${programmeIds}`);
    }
    
    // SME/Applicant filtering
    if (this.isSMEUser(user)) {
      const smeEntityId = user.smeEntityId || user.organizationId;
      if (!smeEntityId) {
        throw new Error('SME user missing SME entity association');
      }
      
      filter.smeEntityId = normalizeObjectId(smeEntityId);
      console.log(`AccessControlService: Applied SME entity filter: ${smeEntityId}`);
    }
    
    // Legacy applicant filtering
    if (this.isApplicantUser(user)) {
      filter.applicantId = user.id;
      console.log(`AccessControlService: Applied applicant filter: ${user.id}`);
    }
    
    return filter;
  }
  
  /**
   * Check if user is admin
   * @param {Object} user - User object
   * @returns {boolean} True if user is admin
   */
  static isAdmin(user) {
    const adminRoles = ['admin', 'manager', 'System Administrator', 'Manager'];
    return user.roles?.some(role => adminRoles.includes(role)) || false;
  }
  
  /**
   * Check if user is corporate sponsor user
   * @param {Object} user - User object
   * @returns {boolean} True if user is corporate sponsor user
   */
  static isCorporateSponsorUser(user) {
    return user.roles?.includes('corporate-sponsor-user') || 
           user.organizationType === 'CorporateSponsor';
  }
  
  /**
   * Check if user is programme user
   * @param {Object} user - User object
   * @returns {boolean} True if user is programme user
   */
  static isProgrammeUser(user) {
    return user.roles?.includes('programme-user') || 
           user.organizationType === 'ServiceProvider';
  }
  
  /**
   * Check if user is SME user
   * @param {Object} user - User object
   * @returns {boolean} True if user is SME user
   */
  static isSMEUser(user) {
    return user.organizationType === 'SME' || 
           user.userType === 'sme_user' ||
           user.roles?.some(role => ['SME_OWNER', 'SME_MANAGER', 'SME_EMPLOYEE', 'SME_APPLICANT'].includes(role));
  }
  
  /**
   * Check if user is applicant user (legacy)
   * @param {Object} user - User object
   * @returns {boolean} True if user is applicant user
   */
  static isApplicantUser(user) {
    return user.roles?.includes('applicant') && !this.isSMEUser(user);
  }
  
  /**
   * Get programme IDs for programme user
   * @param {Object} user - User object
   * @returns {Array} Array of programme IDs
   */
  static getUserProgrammeIds(user) {
    const ids = [];
    
    if (user.fundingProgrammeId) {
      ids.push(user.fundingProgrammeId);
    }
    
    if (user.programmeAssignments?.length > 0) {
      ids.push(...user.programmeAssignments.map(a => a.programmeId));
    }
    
    return [...new Set(ids)]; // Remove duplicates
  }
  
  /**
   * Validate entity access for specific resource
   * @param {Object} user - User object
   * @param {string} resourceType - Type of resource
   * @param {string} resourceId - ID of resource
   * @returns {boolean} True if access is allowed
   */
  static async validateEntityAccess(user, resourceType, resourceId) {
    const filter = await this.getUserEntityFilter(user);
    
    if (Object.keys(filter).length === 0) {
      return true; // Admin user or no filtering required
    }
    
    const Resource = this.getResourceModel(resourceType);
    const resource = await Resource.findById(resourceId);
    
    if (!resource) {
      throw new Error('Resource not found');
    }
    
    return this.resourceMatchesFilter(resource, filter);
  }
  
  /**
   * Get resource model by type
   * @param {string} resourceType - Type of resource
   * @returns {Object} Mongoose model
   */
  static getResourceModel(resourceType) {
    const models = {
      'application': require('../models/application'),
      'corporate-sponsor': require('../models/corporate-sponsor'),
      'funding-programme': require('../models/funding-programme'),
      'service-provider': require('../models/service-provider'),
      'sme-entity': require('../models/sme-entity')
    };
    
    return models[resourceType];
  }
  
  /**
   * Check if resource matches filter
   * @param {Object} resource - Resource object
   * @param {Object} filter - Filter object
   * @returns {boolean} True if resource matches filter
   */
  static resourceMatchesFilter(resource, filter) {
    for (const [key, value] of Object.entries(filter)) {
      if (value && value.$in) {
        // Handle $in queries
        const resourceValue = resource[key];
        if (!resourceValue || !value.$in.some(v => v.toString() === resourceValue.toString())) {
          return false;
        }
      } else {
        // Handle direct comparison
        if (resource[key]?.toString() !== value?.toString()) {
          return false;
        }
      }
    }
    return true;
  }
  
  /**
   * Validate SME entity ownership
   * @param {Object} user - User object
   * @param {string} smeEntityId - SME entity ID to validate
   * @returns {boolean} True if user owns the SME entity
   */
  static validateSMEEntityOwnership(user, smeEntityId) {
    if (!this.isSMEUser(user)) {
      return true; // Non-SME users bypass this check
    }
    
    const userSMEEntityId = user.smeEntityId;
    if (!userSMEEntityId) {
      throw new Error('SME user missing SME entity association');
    }
    
    return userSMEEntityId.toString() === smeEntityId.toString();
  }
}

module.exports = AccessControlService;
