const mongoose = require('mongoose');
require('dotenv').config();

async function checkEntities() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-screening-app');
    
    // Check existing entities
    const CorporateSponsor = require('./src/models/corporate-sponsor');
    const User = require('./src/models/user');
    
    console.log('=== EXISTING CORPORATE SPONSORS ===');
    const sponsors = await CorporateSponsor.find({});
    sponsors.forEach(sponsor => {
      console.log(`ID: ${sponsor._id}, Name: ${sponsor.name}, Type: ${sponsor.organizationType}`);
    });
    
    console.log('\n=== ADMIN USER ===');
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (adminUser) {
      console.log(`ID: ${adminUser._id}`);
      console.log(`Name: ${adminUser.name}`);
      console.log(`Email: ${adminUser.email}`);
      console.log(`Roles: ${JSON.stringify(adminUser.roles)}`);
      console.log(`Organization Type: ${adminUser.organizationType}`);
      console.log(`Organization ID: ${adminUser.organizationId}`);
      console.log(`Corporate Sponsor ID: ${adminUser.corporateSponsorId}`);
    } else {
      console.log('Admin user not found');
    }
    
    await mongoose.disconnect();
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkEntities();
