const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  id: {
    type: String,
    unique: true,
    sparse: true // Allow null values but ensure uniqueness when present
  },
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 50
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: true,
    minlength: 8
  },
  firstName: {
    type: String,
    trim: true
  },
  lastName: {
    type: String,
    trim: true
  },
  // Legacy single role field for backward compatibility
  role: {
    type: String,
    enum: [
      // 20/20 Insight roles
      'SYSTEM_ADMINISTRATOR',
      'MANAGER',
      'LOAN_OFFICER',
      'REVIEWER',
      'READ_ONLY_USER',
      
      // Corporate roles
      'CORPORATE_REVIEWER',
      'CORPORATE_APPROVER',
      
      // Programme roles
      'PROGRAMME_REVIEWER',
      'PROGRAMME_APPROVER',
      
      // Service Provider roles
      'SERVICE_PROVIDER_LOAN_OFFICER',
      'SERVICE_PROVIDER_REVIEWER',
      'SERVICE_PROVIDER_READ_ONLY',
      
      // Auth system roles
      'admin',
      'user'
    ]
  },
  // New roles array for JWT auth system
  roles: {
    type: [String],
    default: function() {
      // Map legacy role to new roles array
      if (this.role) {
        return [this.role.toLowerCase().replace(/_/g, '-'), 'user'];
      }
      return ['user'];
    }
  },
  // New permissions array for fine-grained access control
  permissions: {
    type: [String],
    default: []
  },
  organizationType: {
    type: String,
    enum: ['20/20Insight', 'CorporateSponsor', 'ServiceProvider', 'SmallMediumEnterprise'],
    default: '20/20Insight'
  },
  organizationId: {
    type: String
  },
  // Entity-specific associations for access control
  corporateSponsorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'CorporateSponsor',
    required: false
  },
  fundingProgrammeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FundingProgramme',
    required: false
  },
  programmeAssignments: [{
    programmeId: {
      type: String,
      ref: 'FundingProgramme'
    },
    role: String
  }],
  phone: String,
  profilePicture: String,
  // Legacy status field mapped to isActive
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  },
  // New isActive field for auth system
  isActive: {
    type: Boolean,
    default: function() {
      return this.status === 'active';
    }
  },
  lastLogin: {
    type: Date
  },
  // Password reset fields
  resetPasswordToken: String,
  resetPasswordExpires: Date,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Pre-save middleware to hash password and generate user ID
userSchema.pre('save', async function(next) {
  try {
    // Generate user ID if not present
    if (!this.id) {
      const count = await this.constructor.countDocuments();
      const nextNumber = (count + 1).toString().padStart(3, '0');
      this.id = `USER-2025-${nextNumber}`;
    }
    
    // Hash password if modified
    if (this.isModified('password')) {
      const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 12);
      this.password = await bcrypt.hash(this.password, salt);
    }
    
    // Sync isActive with status
    if (this.isModified('status')) {
      this.isActive = this.status === 'active';
    }
    
    // Sync roles with role for backward compatibility
    if (this.isModified('role') && this.role) {
      const mappedRole = this.role.toLowerCase().replace(/_/g, '-');
      if (!this.roles.includes(mappedRole)) {
        this.roles.push(mappedRole);
      }
      if (!this.roles.includes('user')) {
        this.roles.push('user');
      }
    }
    
    this.updatedAt = Date.now();
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare passwords
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model('User', userSchema);

module.exports = User;
