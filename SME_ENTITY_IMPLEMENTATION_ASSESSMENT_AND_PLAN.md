# SME Entity Implementation Assessment and Technical Plan

**Document Version:** 1.0  
**Date:** January 28, 2025  
**Project:** Funding Screening Portal - SME Entity Implementation  
**Author:** Technical Assessment Team  

---

## Executive Summary

This document provides a comprehensive assessment and technical implementation plan for restructuring the funding screening portal to support SMEs (Small and Medium Enterprises) as first-class entities. The current system embeds SME information within individual applications, leading to data duplication and limiting multi-programme participation. The proposed solution introduces dedicated SME entities, SME user management, and enables multiple funding applications per SME across different programmes.

### Key Recommendations
- **Implement SME Entity Model**: Create dedicated SME profiles as the foundation
- **Introduce SME User Management**: Enable SME users to manage their own applications
- **Enable Multi-Programme Support**: Allow SMEs to register for multiple programmes
- **Phased Migration Approach**: Minimize disruption through careful data migration
- **Estimated Timeline**: 14 weeks for complete implementation

---

## 1. Current System Assessment

### 1.1 Current Architecture Analysis

The existing system follows a **direct application-centric model** with the following characteristics:

#### Data Structure
```
Application (Primary Entity)
├── businessInfo (Embedded)
│   ├── legalName, tradingName
│   ├── registrationNumber, cipcRegistrationNumber
│   ├── entityType, startTradingDate
│   └── industry, businessType, employeeCount
├── personalInfo (Embedded)
│   ├── firstName, lastName, email, phone
│   └── idNumber, address
├── contactDetails (Embedded)
├── financialInfo (Embedded)
├── bbbeeProfile (Embedded)
├── programmeId (Reference)
└── corporateSponsorId (Reference)
```

#### Current User Model
```
User
├── System Users (Loan Officers, Reviewers, Managers)
├── Corporate Sponsor Users (Limited entity access)
├── Programme Users (Programme-specific access)
└── Service Provider Users
```

### 1.2 Identified Limitations

#### 1.2.1 Data Duplication Issues
- **Problem**: SME information is duplicated across multiple applications
- **Impact**: Inconsistent data, maintenance overhead, storage inefficiency
- **Example**: An SME applying to 3 programmes creates 3 separate business profiles

#### 1.2.2 Limited Multi-Programme Support
- **Problem**: No mechanism for SMEs to maintain profiles across programmes
- **Impact**: SMEs must re-enter complete information for each programme
- **Current Workaround**: Manual data entry for each application

#### 1.2.3 No SME User Management
- **Problem**: SMEs cannot directly manage their applications
- **Impact**: All application management must go through system administrators
- **Missing Features**: SME dashboards, self-service capabilities

#### 1.2.4 Scalability Concerns
- **Problem**: Embedded data model doesn't scale with business growth
- **Impact**: Performance degradation as application volume increases
- **Technical Debt**: Increasing complexity in data management

### 1.3 Current System Strengths

#### 1.3.1 Robust Application Workflow
- Well-defined stage hierarchy (Onboarding → Business Case Review → Due Diligence → Assessment Report → Application Approval)
- Comprehensive audit trail system
- Advanced scoring and evaluation mechanisms

#### 1.3.2 Multi-Tenant Architecture
- Support for multiple corporate sponsors
- Programme-specific configurations
- Role-based access control

#### 1.3.3 Document Management
- File upload and storage capabilities
- Document verification workflows
- Integration with application stages

---

## 2. Requirements Analysis

### 2.1 Functional Requirements

#### 2.1.1 SME Entity Management
- **REQ-001**: Create and maintain SME business profiles
- **REQ-002**: Support multiple SME users per entity
- **REQ-003**: Enable SME profile updates and maintenance
- **REQ-004**: Maintain historical profile changes

#### 2.1.2 Multi-Programme Registration
- **REQ-005**: Allow SMEs to register for multiple programmes
- **REQ-006**: Track programme-specific eligibility and status
- **REQ-007**: Support programme-specific requirements and documentation

#### 2.1.3 SME User Portal
- **REQ-008**: SME user registration and authentication
- **REQ-009**: SME dashboard with application overview
- **REQ-010**: Self-service application creation and management
- **REQ-011**: Document upload and management capabilities

#### 2.1.4 Application Management
- **REQ-012**: Create applications linked to SME entities
- **REQ-013**: Pre-populate applications with SME profile data
- **REQ-014**: Support multiple applications per SME per programme
- **REQ-015**: Maintain existing application workflow and approval processes

### 2.2 Non-Functional Requirements

#### 2.2.1 Performance
- **REQ-016**: System response time < 2 seconds for SME portal operations
- **REQ-017**: Support concurrent access by 1000+ SME users
- **REQ-018**: Database query optimization for entity relationships

#### 2.2.2 Security
- **REQ-019**: Secure SME user authentication and authorization
- **REQ-020**: Data privacy compliance for SME information
- **REQ-021**: Audit trail for all SME entity changes

#### 2.2.3 Scalability
- **REQ-022**: Support 10,000+ SME entities
- **REQ-023**: Handle 50,000+ applications with entity references
- **REQ-024**: Horizontal scaling capabilities

---

## 3. Technical Implementation Plan

### 3.1 Implementation Phases Overview

```
Phase 1: SME Entity Foundation (Weeks 1-4)
├── Create SME Entity Model
├── Create SME User Model
├── Update Application Model
└── Database Schema Changes

Phase 2: Data Migration (Weeks 5-6)
├── Migration Script Development
├── Data Validation and Testing
├── SME User Creation
└── Application Reference Updates

Phase 3: API Development (Weeks 7-8)
├── SME Entity API Endpoints
├── SME User Authentication
├── Updated Application APIs
└── Integration Testing

Phase 4: Frontend Development (Weeks 9-12)
├── SME Registration Portal
├── SME User Dashboard
├── Application Management Interface
└── Admin Interface Updates

Phase 5: Testing and Deployment (Weeks 13-14)
├── Comprehensive Testing
├── Performance Optimization
├── Production Deployment
└── Post-Deployment Monitoring
```

### 3.2 Phase 1: SME Entity Foundation (Weeks 1-4)

#### 3.2.1 SME Entity Model

**File: `backend/src/models/sme-entity.js`**

```javascript
const mongoose = require('mongoose');

const addressSchema = new mongoose.Schema({
  street: String,
  city: String,
  province: String,
  postalCode: String,
  country: { type: String, default: 'South Africa' }
});

const bbbeeProfileSchema = new mongoose.Schema({
  bbbeeLevel: { 
    type: String, 
    enum: ['Level 1', 'Level 2', 'Level 3', 'Level 4', 'Level 5', 'Level 6', 'Level 7', 'Level 8', 'Non-Compliant'] 
  },
  bbbeeCertificateExpiryDate: Date,
  blackOwnershipPercentage: { type: Number, min: 0, max: 100 },
  blackWomenOwnershipPercentage: { type: Number, min: 0, max: 100 },
  blackYouthOwnershipPercentage: { type: Number, min: 0, max: 100 },
  blackPeopleWithDisabilityOwnershipPercentage: { type: Number, min: 0, max: 100 },
  bbbeeCategory: String,
  isExxaroSupplier: { type: Boolean, default: false }
});

const smeEntitySchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  
  // Business Registration Information
  businessRegistration: {
    legalName: { type: String, required: true },
    tradingName: { type: String, required: true },
    registrationNumber: String,
    cipcRegistrationNumber: { type: String, required: true },
    entityType: { 
      type: String,
      enum: ['Pty Ltd', 'Close Corporation', 'Non-Profit', 'Partnership', 'Sole Proprietor', 'Other'],
      required: true 
    },
    startTradingDate: { type: Date, required: true },
    cipcRegistrationDocument: String, // File path
    vatNumber: String,
    taxNumber: String,
    industry: String,
    businessType: String,
    yearEstablished: Number,
    employeeCount: Number,
    website: String
  },
  
  // Primary Contact Information
  primaryContact: {
    name: { type: String, required: true },
    surname: { type: String, required: true },
    email: { type: String, required: true, match: /.+\@.+\..+/ },
    mainOfficeNumber: String,
    cellphoneNumber: { type: String, required: true },
    position: String
  },
  
  // Business Address
  businessAddress: addressSchema,
  
  // Financial Profile
  financialProfile: {
    annualTurnover: Number,
    netProfit: Number,
    currentAssets: Number,
    currentLiabilities: Number,
    totalDebt: Number,
    lastFinancialYear: Date,
    taxClearanceExpiryDate: Date,
    taxClearanceCertificateDocument: String, // File path
    vatRegistered: { type: Boolean, default: false },
    vatRegistrationNumber: String,
    vatCertificateDocument: String // File path
  },
  
  // BBBEE Profile
  bbbeeProfile: bbbeeProfileSchema,
  
  // Programme Registrations
  programmeRegistrations: [{
    programmeId: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: 'FundingProgramme',
      required: true
    },
    registrationDate: { type: Date, default: Date.now },
    status: { 
      type: String, 
      enum: ['active', 'inactive', 'suspended'], 
      default: 'active' 
    },
    eligibilityStatus: {
      type: String,
      enum: ['eligible', 'ineligible', 'pending_review'],
      default: 'pending_review'
    },
    notes: String
  }],
  
  // Document Management
  documents: [{
    type: { 
      type: String, 
      enum: [
        'cipc_certificate', 
        'tax_clearance', 
        'vat_certificate', 
        'bbbee_certificate',
        'financial_statements',
        'bank_statements',
        'other'
      ],
      required: true
    },
    filename: { type: String, required: true },
    originalName: String,
    uploadDate: { type: Date, default: Date.now },
    uploadedBy: String, // User ID who uploaded
    status: { 
      type: String, 
      enum: ['pending', 'verified', 'rejected'], 
      default: 'pending' 
    },
    verificationNotes: String,
    verifiedBy: String,
    verificationDate: Date
  }],
  
  // Status and Metadata
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'under_review', 'pending_verification'],
    default: 'pending_verification'
  },
  
  // Verification Status
  verificationStatus: {
    businessRegistration: { type: Boolean, default: false },
    contactDetails: { type: Boolean, default: false },
    financialInformation: { type: Boolean, default: false },
    bbbeeCompliance: { type: Boolean, default: false },
    overallStatus: { 
      type: String, 
      enum: ['pending', 'partial', 'complete'], 
      default: 'pending' 
    }
  },
  
  // Audit Fields
  createdBy: String,
  createdAt: { type: Date, default: Date.now },
  updatedBy: String,
  updatedAt: { type: Date, default: Date.now },
  
  // Change History
  changeHistory: [{
    field: String,
    oldValue: mongoose.Schema.Types.Mixed,
    newValue: mongoose.Schema.Types.Mixed,
    changedBy: String,
    changedAt: { type: Date, default: Date.now },
    reason: String
  }]
});

// Pre-save middleware to generate SME ID
smeEntitySchema.pre('save', async function(next) {
  if (!this.id) {
    const count = await this.constructor.countDocuments();
    const nextNumber = (count + 1).toString().padStart(3, '0');
    this.id = `SME-2025-${nextNumber}`;
  }
  
  // Update verification status
  this.verificationStatus.overallStatus = this.calculateOverallVerificationStatus();
  
  this.updatedAt = Date.now();
  next();
});

// Method to calculate overall verification status
smeEntitySchema.methods.calculateOverallVerificationStatus = function() {
  const statuses = [
    this.verificationStatus.businessRegistration,
    this.verificationStatus.contactDetails,
    this.verificationStatus.financialInformation,
    this.verificationStatus.bbbeeCompliance
  ];
  
  const verifiedCount = statuses.filter(status => status === true).length;
  
  if (verifiedCount === 0) return 'pending';
  if (verifiedCount === statuses.length) return 'complete';
  return 'partial';
};

// Indexes for performance
smeEntitySchema.index({ 'businessRegistration.cipcRegistrationNumber': 1 });
smeEntitySchema.index({ 'businessRegistration.legalName': 'text', 'businessRegistration.tradingName': 'text' });
smeEntitySchema.index({ 'primaryContact.email': 1 });
smeEntitySchema.index({ status: 1 });
smeEntitySchema.index({ 'programmeRegistrations.programmeId': 1 });

const SMEEntity = mongoose.model('SMEEntity', smeEntitySchema);

module.exports = SMEEntity;
```

#### 3.2.2 SME User Model

**File: `backend/src/models/sme-user.js`**

```javascript
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const smeUserSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  
  // Authentication Information
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 50
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: true,
    minlength: 8
  },
  
  // Personal Information
  firstName: { type: String, required: true, trim: true },
  lastName: { type: String, required: true, trim: true },
  phone: String,
  position: String,
  
  // SME Association
  smeEntityId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'SMEEntity', 
    required: true 
  },
  
  // Role within SME
  smeRole: {
    type: String,
    enum: ['owner', 'director', 'manager', 'authorized_user', 'finance_manager'],
    required: true
  },
  
  // Permissions within SME context
  permissions: [{
    type: String,
    enum: [
      'create_applications',
      'edit_applications',
      'submit_applications',
      'view_applications',
      'edit_sme_profile',
      'manage_sme_users',
      'upload_documents',
      'view_financial_data',
      'edit_financial_data'
    ]
  }],
  
  // Account Status
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'pending_verification'],
    default: 'pending_verification'
  },
  
  // Email Verification
  emailVerified: { type: Boolean, default: false },
  emailVerificationToken: String,
  emailVerificationExpires: Date,
  
  // Password Reset
  resetPasswordToken: String,
  resetPasswordExpires: Date,
  
  // Two-Factor Authentication (Future Enhancement)
  twoFactorEnabled: { type: Boolean, default: false },
  twoFactorSecret: String,
  
  // Login Tracking
  lastLogin: Date,
  loginAttempts: { type: Number, default: 0 },
  lockUntil: Date,
  
  // Audit Fields
  createdBy: String,
  createdAt: { type: Date, default: Date.now },
  updatedBy: String,
  updatedAt: { type: Date, default: Date.now }
});

// Pre-save middleware
smeUserSchema.pre('save', async function(next) {
  try {
    // Generate user ID if not present
    if (!this.id) {
      const count = await this.constructor.countDocuments();
      const nextNumber = (count + 1).toString().padStart(3, '0');
      this.id = `SMEU-2025-${nextNumber}`;
    }
    
    // Hash password if modified
    if (this.isModified('password')) {
      const salt = await bcrypt.genSalt(12);
      this.password = await bcrypt.hash(this.password, salt);
    }
    
    this.updatedAt = Date.now();
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare passwords
smeUserSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to check if account is locked
smeUserSchema.methods.isLocked = function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
};

// Method to increment login attempts
smeUserSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked()) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }
  
  return this.updateOne(updates);
};

// Method to reset login attempts
smeUserSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  });
};

// Indexes for performance
smeUserSchema.index({ username: 1 });
smeUserSchema.index({ email: 1 });
smeUserSchema.index({ smeEntityId: 1 });
smeUserSchema.index({ status: 1 });
smeUserSchema.index({ emailVerificationToken: 1 });
smeUserSchema.index({ resetPasswordToken: 1 });

const SMEUser = mongoose.model('SMEUser', smeUserSchema);

module.exports = SMEUser;
```

#### 3.2.3 Updated Application Model

**File: `backend/src/models/application.js` (Key Changes)**

```javascript
// Remove embedded schemas and add SME reference
const applicationSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  
  // SME Entity Reference (NEW)
  smeEntityId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SMEEntity',
    required: true
  },
  
  // Application-specific Information
  programmeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FundingProgramme',
    required: true
  },
  corporateSponsorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'CorporateSponsor',
    required: true
  },
  
  // Application-specific Details (NEW)
  applicationDetails: {
    objective: String,
    fundingPurpose: {
      type: String,
      enum: [
        'Assets Acquisition',
        'Working Capital',
        'Business Expansion',
        'Research & Development',
        'Business Startup',
        'Skills Development',
        'Infrastructure Development',
        'Community Projects',
        'Other'
      ]
    },
    requestedAmount: { type: Number, required: true },
    proposedTimeframe: String,
    expectedOutcomes: [String],
    riskMitigationStrategies: [String]
  },
  
  // Remove: personalInfo, businessInfo, contactDetails, financialInfo, bbbeeProfile
  // These are now referenced from SMEEntity
  
  // Keep existing workflow fields
  currentMainStage: {
    type: String,
    enum: Object.values(ApplicationMainStage),
    default: ApplicationMainStage.ONBOARDING
  },
  currentSubStage: {
    type: String,
    enum: Object.values(ApplicationSubStage),
    default: ApplicationSubStage.BENEFICIARY_REGISTRATION
  },
  currentStageStatus: {
    type: String,
    enum: Object.values(StageStatus),
    default: StageStatus.NOT_STARTED
  },
  
  // ... rest of existing fields (stageHierarchy, status, etc.)
  
  // Enhanced audit trail
  createdBy: {
    userId: String,
    userType: { type: String, enum: ['system_user', 'sme_user'], default: 'sme_user' }
  },
  
  // Application-specific documents (in addition to SME entity documents)
  applicationDocuments: [{
    type: String,
    filename: String,
    uploadDate: { type: Date, default: Date.now },
    uploadedBy: String,
    description: String
  }]
});

// Update pre-save middleware
applicationSchema.pre('save', async function(next) {
  if (!this.id) {
    const count = await this.constructor.countDocuments();
    const nextNumber = (count + 1).toString().padStart(3, '0');
    this.id = `APP-2025-${nextNumber}`;
  }
  next();
});

// Add method to get SME details
applicationSchema.methods.getSMEDetails = async function() {
  await this.populate('smeEntityId');
  return this.smeEntityId;
};

// Add index for SME entity reference
applicationSchema.index({ smeEntityId: 1 });
```

### 3.3 Phase 2: Data Migration (Weeks 5-6)

#### 3.3.1 Migration Script

**File: `backend/src/migrations/migrate-to-sme-entities.js`**

```javascript
const mongoose = require('mongoose');
const Application = require('../models/application');
const SMEEntity = require('../models/sme-entity');
const SMEUser = require('../models/sme-user');
const bcrypt = require('bcryptjs');

const migrateToSMEEntities = async () => {
  console.log('=== Starting SME Entity Migration ===');
  
  try {
    // Step 1: Get all existing applications
    const applications = await Application.find({}).lean();
    console.log(`Found ${applications.length} applications to migrate`);
    
    if (applications.length === 0) {
      console.log('No applications found. Migration completed.');
      return;
    }
    
    // Step 2: Group applications by unique business entities
    const businessGroups = new Map();
    let duplicateCount = 0;
    
    applications.forEach(app => {
      // Create unique key based on CIPC registration number and legal name
      const cipcNumber = app.businessInfo?.cipcRegistrationNumber || 'unknown';
      const legalName = app.businessInfo?.legalName || 'unknown';
      const businessKey = `${cipcNumber}_${legalName}`.toLowerCase().replace(/\s+/g, '_');
      
      if (!businessGroups.has(businessKey)) {
        businessGroups.set(businessKey, {
          applications: [],
          businessInfo: app.businessInfo,
          personalInfo: app.personalInfo,
          contactDetails: app.contactDetails,
          financialInfo: app.financialInfo,
          bbbeeProfile: app.bbbeeProfile,
          programmes: new Set()
        });
      } else {
        duplicateCount++;
      }
      
      const group = businessGroups.get(businessKey);
      group.applications.push(app);
      
      // Track programmes this SME is involved in
      if (app.programmeId) {
        group.programmes.add(app.programmeId.toString());
      }
    });
    
    console.log(`Identified ${businessGroups.size} unique business entities`);
    console.log(`Found ${duplicateCount} duplicate business entities`);
    
    // Step 3: Create SME entities
    const smeEntityMap = new Map(); // Maps application IDs to SME entity IDs
    let createdEntities = 0;
    
    for (const [businessKey, group] of businessGroups) {
      try {
        // Validate required fields
        if (!group.businessInfo?.legalName || !group.businessInfo?.cipcRegistrationNumber) {
          console.warn(`Skipping business entity with missing required fields: ${businessKey}`);
          continue;
        }
        
        // Create programme registrations
        const programmeRegistrations = Array.from(group.programmes).map(progId => ({
          programmeId: new mongoose.Types.ObjectId(progId),
          registrationDate: new Date(),
          status: 'active',
          eligibilityStatus: 'pending_review'
        }));
        
        const smeEntity = new SMEEntity({
          businessRegistration: {
            legalName: group.businessInfo.legalName,
            tradingName: group.businessInfo.tradingName || group.businessInfo.legalName,
            registrationNumber: group.businessInfo.registrationNumber,
            cipcRegistrationNumber: group.businessInfo.cipcRegistrationNumber,
            entityType: group.businessInfo.entityType || 'Pty Ltd',
            startTradingDate: group.businessInfo.startTradingDate || new Date(),
            cipcRegistrationDocument: group.businessInfo.cipcRegistrationDocument,
            vatNumber: group.financialInfo?.vatRegistrationNumber,
            taxNumber: group.financialInfo?.taxNumber,
            industry: group.businessInfo.industry,
            businessType: group.businessInfo.businessType,
            yearEstablished: group.businessInfo.yearEstablished,
            employeeCount: group.businessInfo.employeeCount,
            website: group.businessInfo.website
          },
          primaryContact: {
            name: group.contactDetails?.name || group.personalInfo?.firstName || 'Unknown',
            surname: group.contactDetails?.surname || group.personalInfo?.lastName || 'Unknown',
            email: group.contactDetails?.email || group.personalInfo?.email || `unknown_${Date.now()}@example.com`,
            mainOfficeNumber: group.contactDetails?.mainOfficeNumber,
            cellphoneNumber: group.contactDetails?.cellphoneNumber || group.personalInfo?.phone || 'Unknown',
            position: group.contactDetails?.position || 'Owner'
          },
          businessAddress: group.businessInfo?.address || {},
          financialProfile: {
            annualTurnover: group.financialInfo?.annualTurnover,
            netProfit: group.financialInfo?.netProfit,
            currentAssets: group.financialInfo?.currentAssets,
            currentLiabilities: group.financialInfo?.currentLiabilities,
            totalDebt: group.financialInfo?.totalDebt,
            lastFinancialYear: group.financialInfo?.lastFinancialYear,
            taxClearanceExpiryDate: group.financialInfo?.taxClearanceExpiryDate,
            taxClearanceCertificateDocument: group.financialInfo?.taxClearanceCertificateDocument,
            vatRegistered: group.financialInfo?.vatRegistered || false,
            vatRegistrationNumber: group.financialInfo?.vatRegistrationNumber,
            vatCertificateDocument: group.financialInfo?.vatCertificateDocument
          },
          bbbeeProfile: group.bbbeeProfile || {},
          programmeRegistrations: programmeRegistrations,
          status: 'active',
          verificationStatus: {
            businessRegistration: true, // Assume existing data is verified
            contactDetails: true,
            financialInformation: !!group.financialInfo,
            bbbeeCompliance: !!group.bbbeeProfile,
            overallStatus: 'complete'
          },
          createdBy: 'migration_script',
          createdAt: new Date()
        });
        
        const savedEntity = await smeEntity.save();
        createdEntities++;
        
        // Map all applications in this group to the new SME entity
        group.applications.forEach(app => {
          smeEntityMap.set(app._id.toString(), savedEntity._id);
        });
        
        console.log(`Created SME Entity: ${savedEntity.id} for ${group.applications.length} applications`);
        
        // Create primary SME user for this entity
        await createPrimarySMEUser(savedEntity, group);
        
      } catch (error) {
        console.error(`Error creating SME entity for ${businessKey}:`, error.message);
      }
    }
    
    console.log(`Successfully created ${createdEntities} SME entities`);
    
    // Step 4: Update applications to reference SME entities
    let updatedApplications = 0;
    
    for (const [appId, smeEntityId] of smeEntityMap) {
      try {
        const result = await Application.findByIdAndUpdate(
          appId,
          {
            smeEntityId: smeEntityId,
            $unset: {
              personalInfo: 1,
              businessInfo: 1,
              contactDetails: 1,
              financialInfo: 1,
              bbbeeProfile: 1
            }
          },
          { new: true }
        );
        
        if (result) {
          updatedApplications++;
        }
      } catch (error) {
        console.error(`Error updating application ${appId}:`, error.message);
      }
    }
    
    console.log(`Successfully updated ${updatedApplications} applications`);
    
    // Step 5: Validation
    await validateMigration();
    
    console.log('=== SME Entity Migration Completed Successfully ===');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
};

const createPrimarySMEUser = async (smeEntity, group) => {
  try {
    // Generate username from business name
    const baseUsername = smeEntity.businessRegistration.tradingName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .substring(0, 20);
    
    let username = `${baseUsername}_admin`;
    let counter = 1;
    
    // Ensure unique username
    while (await SMEUser.findOne({ username })) {
      username = `${baseUsername}_admin_${counter}`;
      counter++;
    }
    
    // Generate temporary password
    const tempPassword = `Temp${Math.random().toString(36).substring(2, 10)}!`;
    
    const smeUser = new SMEUser({
      username,
      email: smeEntity.primaryContact.email,
      password: tempPassword, // Will be hashed by pre-save middleware
      firstName: smeEntity.primaryContact.name,
      lastName: smeEntity.primaryContact.surname,
      phone: smeEntity.primaryContact.cellphoneNumber,
      position: smeEntity.primaryContact.position,
      smeEntityId: smeEntity._id,
      smeRole: 'owner',
      permissions: [
        'create_applications',
        'edit_applications',
        'submit_applications',
        'view_applications',
        'edit_sme_profile',
        'manage_sme_users',
        'upload_documents',
        'view_financial_data',
        'edit_financial_data'
      ],
      status: 'active',
      emailVerified: false, // Will need to verify email
      createdBy: 'migration_script'
    });
    
    await smeUser.save();
    console.log(`Created SME User: ${smeUser.id} for entity: ${smeEntity.id}`);
    console.log(`Temporary password: ${tempPassword} (CHANGE IMMEDIATELY)`);
    
  } catch (error) {
    console.error(`Error creating SME user for entity ${smeEntity.id}:`, error.message);
  }
};

const validateMigration = async () => {
  console.log('=== Validating Migration ===');
  
  // Check SME entities
  const smeEntityCount = await SMEEntity.countDocuments();
  console.log(`Total SME entities created: ${smeEntityCount}`);
  
  // Check SME users
  const smeUserCount = await SMEUser.countDocuments();
  console.log(`Total SME users created: ${smeUserCount}`);
  
  // Check applications with SME references
  const appsWithSME = await Application.countDocuments({ smeEntityId: { $exists: true } });
  console.log(`Applications with SME entity references: ${appsWithSME}`);
  
  // Check for orphaned applications
  const orphanedApps = await Application.countDocuments({ 
    smeEntityId: { $exists: false },
    businessInfo: { $exists: true }
  });
  
  if (orphanedApps > 0) {
    console.warn(`WARNING: ${orphanedApps} applications still have embedded business info`);
  }
  
  // Validate data integrity
  const sampleApp = await Application.findOne({ smeEntityId: { $exists: true } })
    .populate('smeEntityId');
  
  if (sampleApp && sampleApp.smeEntityId) {
    console.log('✓ Sample application successfully references SME entity');
    console.log(`  SME: ${sampleApp.smeEntityId.businessRegistration.legalName}`);
  } else {
    console.error('✗ Sample application validation failed');
  }
  
  console.log('=== Migration Validation Complete ===');
};

// Export the migration function
module.exports = { migrateToSMEEntities, validateMigration };
```

#### 3.3.2 Rollback Script

**File: `backend/src/migrations/rollback-sme-migration.js`**

```javascript
const mongoose = require('mongoose');
const Application = require('../models/application');
const SMEEntity = require('../models/sme-entity');
const SMEUser = require('../models/sme-user');

const rollbackSMEMigration = async () => {
  console.log('=== Starting SME Migration Rollback ===');
  
  try {
    // Step 1: Get all applications with SME entity references
    const applications = await Application.find({ smeEntityId: { $exists: true } })
      .populate('smeEntityId')
      .lean();
    
    console.log(`Found ${applications.length} applications to rollback`);
    
    // Step 2: Restore embedded business data
    let restoredCount = 0;
    
    for (const app of applications) {
      if (!app.smeEntityId) {
        console.warn(`Application ${app.id} has no SME entity reference`);
        continue;
      }
      
      const smeEntity = app.smeEntityId;
      
      try {
        await Application.findByIdAndUpdate(app._id, {
          personalInfo: {
            firstName: smeEntity.primaryContact.name,
            lastName: smeEntity.primaryContact.surname,
            email: smeEntity.primaryContact.email,
            phone: smeEntity.primaryContact.cellphoneNumber,
            address: smeEntity.businessAddress
          },
          businessInfo: {
            ...smeEntity.businessRegistration,
            address: smeEntity.businessAddress
          },
          contactDetails: smeEntity.primaryContact,
          financialInfo: smeEntity.financialProfile,
          bbbeeProfile: smeEntity.bbbeeProfile,
          $unset: { smeEntityId: 1 }
        });
        
        restoredCount++;
      } catch (error) {
        console.error(`Error restoring application ${app.id}:`, error.message);
      }
    }
    
    console.log(`Successfully restored ${restoredCount} applications`);
    
    // Step 3: Remove SME entities and users (with confirmation)
    console.log('WARNING: About to delete all SME entities and users');
    console.log('This action cannot be undone. Proceeding in 5 seconds...');
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const deletedUsers = await SMEUser.deleteMany({});
    console.log(`Deleted ${deletedUsers.deletedCount} SME users`);
    
    const deletedEntities = await SMEEntity.deleteMany({});
    console.log(`Deleted ${deletedEntities.deletedCount} SME entities`);
    
    console.log('=== SME Migration Rollback Completed ===');
    
  } catch (error) {
    console.error('Rollback failed:', error);
    throw error;
  }
};

module.exports = { rollbackSMEMigration };
```

### 3.4 Phase 3: API Development (Weeks 7-8)

#### 3.4.1 SME Entity Routes

**File: `backend/src/routes/sme-entities.js`**

```javascript
const express = require('express');
const router = express.Router();
const SMEEntity = require('../models/sme-entity');
const SMEUser = require('../models/sme-user');
const Application = require('../models/application');
const { authenticateToken, checkSMEAccess } = require('../middleware/auth');

// GET /api/sme-entities - List SME entities (admin only)
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 25, status, search } = req.query;
    const filter = {};
    
    if (status) filter.status = status;
    if (search) {
      filter.$or = [
        { 'businessRegistration.legalName': new RegExp(search, 'i') },
        { 'businessRegistration.tradingName': new RegExp(search, 'i') },
        { 'businessRegistration.cipcRegistrationNumber': new RegExp(search, 'i') }
      ];
    }
    
    const entities = await SMEEntity.find(filter)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('programmeRegistrations.programmeId')
      .sort({ createdAt: -1 });
    
    const total = await SMEEntity.countDocuments(filter);
    
    res.json({
      entities,
      total,
      page: Number(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch SME entities', error: error.message });
  }
});

// GET /api/sme-entities/:id - Get SME entity details
router.get('/:id', authenticateToken, checkSMEAccess, async (req, res) => {
  try {
    const entity = await SMEEntity.findById(req.params.id)
      .populate('programmeRegistrations.programmeId');
    
    if (!entity) {
      return res.status(404).json({ message: 'SME entity not found' });
    }
    
    res.json(entity);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch SME entity', error: error.message });
  }
});

// PUT /api/sme-entities/:id - Update SME entity
router.put('/:id', authenticateToken, checkSMEAccess, async (req, res) => {
  try {
    const entity = await SMEEntity.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.user.id },
      { new: true, runValidators: true }
    );
    
    if (!entity) {
      return res.status(404).json({ message: 'SME entity not found' });
    }
    
    res.json(entity);
  } catch (error) {
    res.status(500).json({ message: 'Failed to update SME entity', error: error.message });
  }
});

// GET /api/sme-entities/:id/applications - Get applications for SME entity
router.get('/:id/applications', authenticateToken, checkSMEAccess, async (req, res) => {
  try {
    const applications = await Application.find({ smeEntityId: req.params.id })
      .populate('programmeId')
      .populate('corporateSponsorId')
      .sort({ createdAt: -1 });
    
    res.json(applications);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch applications', error: error.message });
  }
});

// POST /api/sme-entities/:id/programme-registrations - Register for programme
router.post('/:id/programme-registrations', authenticateToken, checkSMEAccess, async (req, res) => {
  try {
    const { programmeId } = req.body;
    
    const entity = await SMEEntity.findById(req.params.id);
    if (!entity) {
      return res.status(404).json({ message: 'SME entity not found' });
    }
    
    // Check if already registered
    const existingRegistration = entity.programmeRegistrations.find(
      reg => reg.programmeId.toString() === programmeId
    );
    
    if (existingRegistration) {
      return res.status(400).json({ message: 'Already registered for this programme' });
    }
    
    entity.programmeRegistrations.push({
      programmeId,
      registrationDate: new Date(),
      status: 'active',
      eligibilityStatus: 'pending_review'
    });
    
    await entity.save();
    
    res.json({ message: 'Successfully registered for programme', entity });
  } catch (error) {
    res.status(500).json({ message: 'Failed to register for programme', error: error.message });
  }
});

module.exports = router;
```

#### 3.4.2 SME User Routes

**File: `backend/src/routes/sme-users.js`**

```javascript
const express = require('express');
const router = express.Router();
const SMEUser = require('../models/sme-user');
const SMEEntity = require('../models/sme-entity');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { authenticateToken } = require('../middleware/auth');

// POST /api/sme-users/register - SME user registration
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, firstName, lastName, phone, position, smeEntityId, smeRole } = req.body;
    
    // Validate SME entity exists
    const smeEntity = await SMEEntity.findById(smeEntityId);
    if (!smeEntity) {
      return res.status(400).json({ message: 'Invalid SME entity' });
    }
    
    // Check if user already exists
    const existingUser = await SMEUser.findOne({ $or: [{ username }, { email }] });
    if (existingUser) {
      return res.status(400).json({ message: 'Username or email already exists' });
    }
    
    // Generate email verification token
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');
    
    const smeUser = new SMEUser({
      username,
      email,
      password,
      firstName,
      lastName,
      phone,
      position,
      smeEntityId,
      smeRole,
      permissions: getDefaultPermissions(smeRole),
      emailVerificationToken,
      emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    });
    
    await smeUser.save();
    
    // TODO: Send verification email
    
    res.status(201).json({
      message: 'User registered successfully. Please check your email for verification.',
      userId: smeUser.id
    });
  } catch (error) {
    res.status(500).json({ message: 'Registration failed', error: error.message });
  }
});

// POST /api/sme-users/login - SME user login
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    const user = await SMEUser.findOne({ username }).populate('smeEntityId');
    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }
    
    // Check if account is locked
    if (user.isLocked()) {
      return res.status(423).json({ message: 'Account temporarily locked due to too many failed login attempts' });
    }
    
    const isValidPassword = await user.comparePassword(password);
    if (!isValidPassword) {
      await user.incLoginAttempts();
      return res.status(401).json({ message: 'Invalid credentials' });
    }
    
    // Reset login attempts on successful login
    if (user.loginAttempts > 0) {
      await user.resetLoginAttempts();
    }
    
    // Update last login
    user.lastLogin = new Date();
    await user.save();
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        smeEntityId: user.smeEntityId._id,
        userType: 'sme_user',
        permissions: user.permissions
      },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        smeRole: user.smeRole,
        permissions: user.permissions,
        smeEntity: user.smeEntityId
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Login failed', error: error.message });
  }
});

// GET /api/sme-users/profile - Get current user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = await SMEUser.findById(req.user.userId)
      .populate('smeEntityId')
      .select('-password');
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    res.json(user);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch profile', error: error.message });
  }
});

// Helper function to get default permissions based on role
function getDefaultPermissions(role) {
  const permissionMap = {
    owner: [
      'create_applications',
      'edit_applications',
      'submit_applications',
      'view_applications',
      'edit_sme_profile',
      'manage_sme_users',
      'upload_documents',
      'view_financial_data',
      'edit_financial_data'
    ],
    director: [
      'create_applications',
      'edit_applications',
      'submit_applications',
      'view_applications',
      'edit_sme_profile',
      'upload_documents',
      'view_financial_data',
      'edit_financial_data'
    ],
    manager: [
      'create_applications',
      'edit_applications',
      'view_applications',
      'upload_documents',
      'view_financial_data'
    ],
    authorized_user: [
      'view_applications',
      'upload_documents'
    ],
    finance_manager: [
      'view_applications',
      'upload_documents',
      'view_financial_data',
      'edit_financial_data'
    ]
  };
  
  return permissionMap[role] || ['view_applications'];
}

module.exports = router;
```

### 3.5 Phase 4: Frontend Development (Weeks 9-12)

#### 3.5.1 SME Registration Portal Components

**Key Components to Develop:**
- SME Entity Registration Form
- SME User Registration Form
- Email Verification Interface
- SME Dashboard
- Application Management Interface

#### 3.5.2 Updated Application Creation Flow

**Changes Required:**
- Pre-populate forms with SME entity data
- Remove embedded business information forms
- Add application-specific fields only
- Enable multiple applications per programme

### 3.6 Phase 5: Testing and Deployment (Weeks 13-14)

#### 3.6.1 Testing Strategy

**Unit Tests:**
- SME Entity model validation
- SME User authentication
- Migration script functionality
- API endpoint responses

**Integration Tests:**
- End-to-end application creation flow
- SME user registration and login
- Multi-programme registration
- Data migration validation

**Performance Tests:**
- Database query optimization
- Concurrent user access
- Large dataset handling

---

## 4. API Design Standards and Documentation

### 4.1 RESTful API Design Principles

#### 4.1.1 Core Principles
- **Resource-Based URLs**: URLs should represent resources, not actions
- **HTTP Methods**: Use appropriate HTTP methods for different operations
- **Stateless**: Each request should contain all information needed to process it
- **Consistent Naming**: Follow consistent naming conventions across all endpoints
- **Hierarchical Structure**: Use logical resource hierarchies

#### 4.1.2 Resource Modeling
```
/api/v1/sme-entities/{id}
/api/v1/sme-entities/{id}/users
/api/v1/sme-entities/{id}/applications
/api/v1/sme-entities/{id}/programme-registrations
/api/v1/sme-users/{id}
/api/v1/applications/{id}
/api/v1/funding-programmes/{id}
```

### 4.2 URL Structure and Naming Conventions

#### 4.2.1 URL Structure Standards
```
https://api.fundingportal.com/api/v1/{resource}/{id}/{sub-resource}
```

**Components:**
- **Base URL**: `https://api.fundingportal.com`
- **API Prefix**: `/api`
- **Version**: `/v1`
- **Resource**: Plural noun (e.g., `sme-entities`, `applications`)
- **Identifier**: Resource ID (e.g., `SME-2025-001`, `APP-2025-001`)
- **Sub-resource**: Related resources (e.g., `users`, `applications`)

#### 4.2.2 Naming Conventions
- **Resources**: Use plural nouns with kebab-case (`sme-entities`, `funding-programmes`)
- **Query Parameters**: Use camelCase (`pageSize`, `sortBy`, `filterBy`)
- **JSON Fields**: Use camelCase (`firstName`, `businessRegistration`, `programmeId`)
- **HTTP Headers**: Use standard header names with proper casing

#### 4.2.3 URL Examples
```
GET    /api/v1/sme-entities
POST   /api/v1/sme-entities
GET    /api/v1/sme-entities/SME-2025-001
PUT    /api/v1/sme-entities/SME-2025-001
DELETE /api/v1/sme-entities/SME-2025-001
GET    /api/v1/sme-entities/SME-2025-001/applications
POST   /api/v1/sme-entities/SME-2025-001/programme-registrations
```

### 4.3 HTTP Methods and Status Codes

#### 4.3.1 HTTP Methods
| Method | Purpose | Idempotent | Safe |
|--------|---------|------------|------|
| GET | Retrieve resources | Yes | Yes |
| POST | Create new resources | No | No |
| PUT | Update/replace entire resource | Yes | No |
| PATCH | Partial update of resource | No | No |
| DELETE | Remove resource | Yes | No |

#### 4.3.2 HTTP Status Codes
**Success Codes:**
- `200 OK` - Successful GET, PUT, PATCH, DELETE
- `201 Created` - Successful POST with resource creation
- `202 Accepted` - Request accepted for async processing
- `204 No Content` - Successful DELETE or PUT with no response body

**Client Error Codes:**
- `400 Bad Request` - Invalid request syntax or validation errors
- `401 Unauthorized` - Authentication required or failed
- `403 Forbidden` - Authenticated but not authorized
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict (e.g., duplicate email)
- `422 Unprocessable Entity` - Validation errors with details

**Server Error Codes:**
- `500 Internal Server Error` - Unexpected server error
- `502 Bad Gateway` - Invalid response from upstream server
- `503 Service Unavailable` - Server temporarily unavailable

### 4.4 Request/Response Format Standards

#### 4.4.1 Content Type
- **Request**: `Content-Type: application/json`
- **Response**: `Content-Type: application/json; charset=utf-8`
- **File Upload**: `Content-Type: multipart/form-data`

#### 4.4.2 Request Format
```json
{
  "data": {
    "businessRegistration": {
      "legalName": "Example Business Pty Ltd",
      "tradingName": "Example Business",
      "cipcRegistrationNumber": "2023/123456/07",
      "entityType": "Pty Ltd"
    },
    "primaryContact": {
      "name": "John",
      "surname": "Doe",
      "email": "<EMAIL>",
      "cellphoneNumber": "+***********"
    }
  }
}
```

#### 4.4.3 Response Format
**Success Response:**
```json
{
  "success": true,
  "data": {
    "id": "SME-2025-001",
    "businessRegistration": {
      "legalName": "Example Business Pty Ltd",
      "tradingName": "Example Business",
      "cipcRegistrationNumber": "2023/123456/07",
      "entityType": "Pty Ltd"
    },
    "status": "active",
    "createdAt": "2025-01-28T10:00:00Z",
    "updatedAt": "2025-01-28T10:00:00Z"
  },
  "meta": {
    "timestamp": "2025-01-28T10:00:00Z",
    "version": "1.0",
    "requestId": "req-12345"
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed for the provided data",
    "details": [
      {
        "field": "businessRegistration.legalName",
        "message": "Legal name is required",
        "code": "REQUIRED_FIELD"
      },
      {
        "field": "primaryContact.email",
        "message": "Invalid email format",
        "code": "INVALID_FORMAT"
      }
    ]
  },
  "meta": {
    "timestamp": "2025-01-28T10:00:00Z",
    "version": "1.0",
    "requestId": "req-12345"
  }
}
```

#### 4.4.4 Pagination Format
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "pageSize": 25,
    "totalItems": 150,
    "totalPages": 6,
    "hasNext": true,
    "hasPrevious": false
  },
  "meta": {
    "timestamp": "2025-01-28T10:00:00Z",
    "version": "1.0",
    "requestId": "req-12345"
  }
}
```

### 4.5 Authentication and Authorization

#### 4.5.1 Authentication Standards
**JWT Token Structure:**
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "sub": "SMEU-2025-001",
    "iat": 1643723400,
    "exp": 1643809800,
    "userType": "sme_user",
    "smeEntityId": "SME-2025-001",
    "permissions": ["create_applications", "view_applications"],
    "roles": ["owner"]
  }
}
```

**Authentication Headers:**
```
Authorization: Bearer <jwt-token>
X-API-Key: <api-key> (for system-to-system calls)
```

#### 4.5.2 Authorization Patterns
**Resource-Based Authorization:**
```javascript
// Check if user can access SME entity
if (req.user.userType === 'sme_user' && req.user.smeEntityId !== req.params.smeEntityId) {
  return res.status(403).json({
    success: false,
    error: {
      code: 'FORBIDDEN',
      message: 'Access denied to this SME entity'
    }
  });
}
```

**Permission-Based Authorization:**
```javascript
// Check specific permissions
const requiredPermission = 'edit_sme_profile';
if (!req.user.permissions.includes(requiredPermission)) {
  return res.status(403).json({
    success: false,
    error: {
      code: 'INSUFFICIENT_PERMISSIONS',
      message: `Required permission: ${requiredPermission}`
    }
  });
}
```

### 4.6 Error Handling Standards

#### 4.6.1 Error Categories
**Validation Errors (400):**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Request validation failed",
    "details": [
      {
        "field": "email",
        "message": "Email is required",
        "code": "REQUIRED_FIELD",
        "value": null
      }
    ]
  }
}
```

**Authentication Errors (401):**
```json
{
  "success": false,
  "error": {
    "code": "AUTHENTICATION_FAILED",
    "message": "Invalid or expired token",
    "details": {
      "reason": "TOKEN_EXPIRED",
      "expiredAt": "2025-01-28T09:00:00Z"
    }
  }
}
```

**Authorization Errors (403):**
```json
{
  "success": false,
  "error": {
    "code": "FORBIDDEN",
    "message": "Insufficient permissions to access this resource",
    "details": {
      "required": ["edit_sme_profile"],
      "provided": ["view_applications"]
    }
  }
}
```

**Resource Not Found (404):**
```json
{
  "success": false,
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "SME entity not found",
    "details": {
      "resource": "sme-entity",
      "identifier": "SME-2025-999"
    }
  }
}
```

#### 4.6.2 Error Handling Middleware
```javascript
const errorHandler = (err, req, res, next) => {
  const errorResponse = {
    success: false,
    error: {
      code: err.code || 'INTERNAL_ERROR',
      message: err.message || 'An unexpected error occurred'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: '1.0',
      requestId: req.id
    }
  };

  // Add validation details for validation errors
  if (err.name === 'ValidationError') {
    errorResponse.error.details = Object.values(err.errors).map(e => ({
      field: e.path,
      message: e.message,
      code: 'VALIDATION_ERROR',
      value: e.value
    }));
  }

  // Log error for monitoring
  logger.error('API Error', {
    error: err,
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body
    }
  });

  res.status(err.statusCode || 500).json(errorResponse);
};
```

### 4.7 API Versioning Strategy

#### 4.7.1 Versioning Approach
**URL Path Versioning (Recommended):**
```
/api/v1/sme-entities
/api/v2/sme-entities
```

**Header Versioning (Alternative):**
```
Accept: application/vnd.fundingportal.v1+json
API-Version: 1.0
```

#### 4.7.2 Version Lifecycle
- **v1.0**: Initial release
- **v1.1**: Backward-compatible additions
- **v2.0**: Breaking changes
- **Deprecation**: 6-month notice before removal
- **Support**: Maintain 2 major versions simultaneously

#### 4.7.3 Breaking vs Non-Breaking Changes
**Non-Breaking Changes:**
- Adding new optional fields
- Adding new endpoints
- Adding new query parameters
- Expanding enum values

**Breaking Changes:**
- Removing fields or endpoints
- Changing field types
- Making optional fields required
- Changing URL structure

### 4.8 OpenAPI/Swagger Documentation

#### 4.8.1 OpenAPI Specification Structure
```yaml
openapi: 3.0.3
info:
  title: SME Entity Management API
  description: API for managing SME entities, users, and applications
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.fundingportal.com/api/v1
    description: Production server
  - url: https://staging-api.fundingportal.com/api/v1
    description: Staging server

security:
  - BearerAuth: []

paths:
  /sme-entities:
    get:
      summary: List SME entities
      description: Retrieve a paginated list of SME entities
      tags:
        - SME Entities
      parameters:
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 25
        - name: status
          in: query
          description: Filter by status
          schema:
            type: string
            enum: [active, inactive, suspended, under_review, pending_verification]
        - name: search
          in: query
          description: Search term for legal name, trading name, or CIPC number
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SMEEntityListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    SMEEntity:
      type: object
      required:
        - id
        - businessRegistration
        - primaryContact
        - status
      properties:
        id:
          type: string
          pattern: '^SME-\d{4}-\d{3}$'
          example: 'SME-2025-001'
        businessRegistration:
          $ref: '#/components/schemas/BusinessRegistration'
        primaryContact:
          $ref: '#/components/schemas/PrimaryContact'
        status:
          type: string
          enum: [active, inactive, suspended, under_review, pending_verification]
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    BusinessRegistration:
      type: object
      required:
        - legalName
        - tradingName
        - cipcRegistrationNumber
        - entityType
        - startTradingDate
      properties:
        legalName:
          type: string
          maxLength: 255
          example: 'Example Business Pty Ltd'
        tradingName:
          type: string
          maxLength: 255
          example: 'Example Business'
        cipcRegistrationNumber:
          type: string
          pattern: '^\d{4}/\d{6}/\d{2}$'
          example: '2023/123456/07'
        entityType:
          type: string
          enum: ['Pty Ltd', 'Close Corporation', 'Non-Profit', 'Partnership', 'Sole Proprietor', 'Other']
        startTradingDate:
          type: string
          format: date

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
```

#### 4.8.2 Documentation Requirements
**Endpoint Documentation Must Include:**
- Clear description of purpose
- All parameters with types and constraints
- Request/response examples
- Error scenarios and codes
- Authentication requirements
- Rate limiting information

**Schema Documentation Must Include:**
- Field descriptions and constraints
- Required vs optional fields
- Data types and formats
- Validation rules
- Example values

### 4.9 Input Validation and Sanitization

#### 4.9.1 Validation Standards
**Field Validation Rules:**
```javascript
const smeEntityValidation = {
  'businessRegistration.legalName': {
    required: true,
    type: 'string',
    maxLength: 255,
    pattern: /^[a-zA-Z0-9\s\-\(\)\.]+$/
  },
  'businessRegistration.cipcRegistrationNumber': {
    required: true,
    type: 'string',
    pattern: /^\d{4}\/\d{6}\/\d{2}$/
  },
  'primaryContact.email': {
    required: true,
    type: 'email',
    maxLength: 255
  },
  'primaryContact.cellphoneNumber': {
    required: true,
    type: 'string',
    pattern: /^\+27\d{9}$/
  }
};
```

**Validation Middleware:**
```javascript
const validateRequest = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        code: 'VALIDATION_ERROR',
        value: detail.context.value
      }));

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Request validation failed',
          details: validationErrors
        }
      });
    }

    req.validatedBody = value;
    next();
  };
};
```

#### 4.9.2 Sanitization Standards
**Input Sanitization:**
```javascript
const sanitizeInput = (data) => {
  if (typeof data === 'string') {
    return data
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .substring(0, 1000); // Limit length
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return data;
};
```

### 4.10 Performance and Rate Limiting

#### 4.10.1 Performance Standards
**Response Time Requirements:**
- **GET requests**: < 200ms for single resources, < 500ms for collections
- **POST/PUT requests**: < 1000ms
- **Complex operations**: < 2000ms
- **File uploads**: < 5000ms

**Optimization Strategies:**
- Database query optimization with proper indexing
- Response caching for frequently accessed data
- Pagination for large datasets
- Compression for large responses
- CDN for static assets

#### 4.10.2 Rate Limiting
**Rate Limit Tiers:**
```javascript
const rateLimits = {
  sme_user: {
    requests: 1000,
    window: '1h',
    burst: 50
  },
  system_user: {
    requests: 5000,
    window: '1h',
    burst: 100
  },
  admin: {
    requests: 10000,
    window: '1h',
    burst: 200
  }
};
```

**Rate Limiting Headers:**
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1643809800
X-RateLimit-Window: 3600
```

#### 4.10.3 Caching Strategy
**Cache Headers:**
```
Cache-Control: public, max-age=300
ETag: "abc123"
Last-Modified: Wed, 28 Jan 2025 10:00:00 GMT
```

**Caching Rules:**
- **Static data**: 1 hour cache
- **User-specific data**: No cache
- **Public lists**: 5 minutes cache
- **File downloads**: 24 hours cache

### 4.11 API Testing Standards

#### 4.11.1 Test Categories
**Unit Tests:**
- Model validation
- Business logic functions
- Utility functions
- Middleware functions

**Integration Tests:**
- API endpoint functionality
- Database operations
- Authentication flows
- Authorization checks

**Contract Tests:**
- API schema validation
- Request/response format verification
- Backward compatibility checks

#### 4.11.2 Test Structure
```javascript
describe('SME Entity API', () => {
  describe('POST /api/v1/sme-entities', () => {
    it('should create a new SME entity with valid data', async () => {
      const validData = {
        businessRegistration: {
          legalName: 'Test Business Pty Ltd',
          tradingName: 'Test Business',
          cipcRegistrationNumber: '2023/123456/07',
          entityType: 'Pty Ltd',
          startTradingDate: '2023-01-01'
        },
        primaryContact: {
          name: 'John',
          surname: 'Doe',
          email: '<EMAIL>',
          cellphoneNumber: '+***********'
        }
      };

      const response = await request(app)
        .post('/api/v1/sme-entities')
        .set('Authorization', `Bearer ${validToken}`)
        .send({ data: validData })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toMatch(/^SME-\d{4}-\d{3}$/);
      expect(response.body.data.businessRegistration.legalName).toBe(validData.businessRegistration.legalName);
    });

    it('should return validation error for missing required fields', async () => {
      const invalidData = {
        businessRegistration: {
          legalName: 'Test Business Pty Ltd'
          // Missing required fields
        }
      };

      const response = await request(app)
        .post('/api/v1/sme-entities')
        .set('Authorization', `Bearer ${validToken}`)
        .send({ data: invalidData })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
      expect(response.body.error.details).toHaveLength(4); // 4 missing required fields
    });
  });
});
```

### 4.12 API Security Standards

#### 4.12.1 Security Headers
**Required Security Headers:**
```javascript
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  res.setHeader('Content-Security-Policy', "default-src 'self'");
  next();
});
```

#### 4.12.2 Input Security
**SQL Injection Prevention:**
- Use parameterized queries
- Input validation and sanitization
- ORM/ODM with built-in protection

**XSS Prevention:**
- Output encoding
- Content Security Policy
- Input sanitization

**CSRF Prevention:**
- CSRF tokens for state-changing operations
- SameSite cookie attributes
- Origin header validation

### 4.13 Monitoring and Logging

#### 4.13.1 API Metrics
**Key Metrics to Track:**
- Request count by endpoint
- Response times (p50, p95, p99)
- Error rates by status code
- Authentication failures
- Rate limit violations

#### 4.13.2 Logging Standards
**Log Format:**
```json
{
  "timestamp": "2025-01-28T10:00:00Z",
  "level": "info",
  "message": "API request processed",
  "requestId": "req-12345",
  "method": "POST",
  "url": "/api/v1/sme-entities",
  "statusCode": 201,
  "responseTime": 150,
  "userId": "SMEU-2025-001",
  "userAgent": "Mozilla/5.0...",
  "ip": "***********"
}
```

**Log Levels:**
- **ERROR**: System errors, exceptions
- **WARN**: Authentication failures, validation errors
- **INFO**: Successful requests, business events
- **DEBUG**: Detailed execution flow (development only)

---

## 5. Risk Assessment and Mitigation

### 4.1 Technical Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|---------|-------------|-------------------|
| Data Loss During Migration | High | Low | Complete backup, staged rollout, rollback scripts |
| Performance Degradation | Medium | Medium | Database optimization, caching, load testing |
| Authentication Issues | High | Low | Thorough testing, gradual user migration |
| Integration Failures | Medium | Medium | Comprehensive integration testing |

### 4.2 Business Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|---------|-------------|-------------------|
| User Adoption Resistance | Medium | Medium | Training, gradual rollout, support documentation |
| Workflow Disruption | High | Low | Parallel systems during transition |
| Data Inconsistency | High | Low | Validation scripts, data integrity checks |

---

## 5. Success Criteria

### 5.1 Technical Success Metrics
- ✅ 100% data migration without loss
- ✅ System response time < 2 seconds
- ✅ Zero critical bugs in production
- ✅ 99.9% system uptime

### 5.2 Business Success Metrics
- ✅ SME users can self-register and manage profiles
- ✅ Multiple applications per SME supported
- ✅ 50% reduction in duplicate data entry
- ✅ Improved user satisfaction scores

### 5.3 User Acceptance Criteria
- ✅ SME users can create and manage their profiles
- ✅ Applications pre-populate with SME data
- ✅ Multi-programme registration works seamlessly
- ✅ Existing workflows remain functional

---

## 6. Timeline and Milestones

### Week 1-2: Foundation Setup
- [ ] Create SME Entity model
- [ ] Create SME User model
- [ ] Update Application model
- [ ] Database schema changes

### Week 3-4: Model Integration
- [ ] Update existing models
- [ ] Create relationships
- [ ] Add validation rules
- [ ] Performance optimization

### Week 5-6: Data Migration
- [ ] Develop migration scripts
- [ ] Test migration in staging
- [ ] Create rollback procedures
- [ ] Validate data integrity

### Week 7-8: API Development
- [ ] SME Entity endpoints
- [ ] SME User authentication
- [ ] Updated Application APIs
- [ ] Integration testing

### Week 9-10: Frontend Foundation
- [ ] SME registration portal
- [ ] User authentication flows
- [ ] Basic dashboard structure
- [ ] Navigation updates

### Week 11-12: Frontend Completion
- [ ] Application management interface
- [ ] Admin interface updates
- [ ] User experience optimization
- [ ] Cross-browser testing

### Week 13-14: Testing and Deployment
- [ ] Comprehensive testing
- [ ] Performance optimization
- [ ] Production deployment
- [ ] Post-deployment monitoring

---

## 7. Post-Implementation Considerations

### 7.1 Monitoring and Maintenance
- Set up monitoring for SME entity operations
- Regular data integrity checks
- Performance monitoring and optimization
- User feedback collection and analysis

### 7.2 Future Enhancements
- Advanced SME analytics and reporting
- Integration with external business registries
- Mobile application for SME users
- AI-powered application assistance

### 7.3 Training and Support
- SME user training materials
- Administrator training sessions
- Technical documentation updates
- Support ticket system enhancements

---

## 8. Conclusion

The implementation of SME entities represents a significant architectural improvement to the funding screening portal. By creating dedicated SME profiles and enabling self-service capabilities, the system will become more scalable, efficient, and user-friendly.

The phased approach ensures minimal disruption to existing operations while providing a clear path to the enhanced functionality. With proper execution of this plan, the system will be well-positioned to support growing numbers of SMEs and funding programmes.

**Next Steps:**
1. Stakeholder approval of this implementation plan
2. Resource allocation and team assignment
3. Development environment setup
4. Commencement of Phase 1 development

---

**Document Control:**
- **Version:** 1.0
- **Last Updated:** January 28, 2025
- **Next Review:** February 28, 2025
- **Approved By:** [Pending]
