<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Horizontal Scroll - User Roles Table</title>
    <style>
        /* Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        /* Container structure */
        .container-fluid {
            height: 100vh;
            overflow: hidden;
            position: relative;
            padding: 0;
        }

        .row {
            height: 100%;
            display: flex;
            flex-wrap: nowrap;
            margin: 0;
        }

        /* Sidebar */
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            flex-shrink: 0;
        }

        /* Main content with horizontal scroll */
        .user-role-container {
            flex: 1;
            height: 100%;
            overflow-x: scroll !important;
            overflow-y: auto !important;
            padding: 2rem;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            position: relative;
        }

        /* Force scrollbar visibility */
        .user-role-container::-webkit-scrollbar {
            height: 16px !important;
            width: 16px !important;
        }

        .user-role-container::-webkit-scrollbar-track {
            background: #f1f1f1 !important;
            border-radius: 8px !important;
            border: 1px solid #ddd;
        }

        .user-role-container::-webkit-scrollbar-thumb {
            background: #888 !important;
            border-radius: 8px !important;
            border: 2px solid #f1f1f1;
        }

        .user-role-container::-webkit-scrollbar-thumb:hover {
            background: #555 !important;
        }

        /* Content wrapper with minimum width */
        .content-wrapper {
            min-width: 1600px !important;
            width: max-content;
        }

        /* All direct children have minimum width */
        .user-role-container > * {
            min-width: 1600px !important;
        }

        /* Card styles */
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-width: 100%;
        }

        /* Table styles */
        .table-responsive {
            overflow-x: visible !important;
            overflow-y: visible !important;
        }

        table {
            width: 100%;
            min-width: 1800px !important;
            border-collapse: collapse;
            table-layout: fixed;
        }

        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
            min-width: 80px;
            white-space: nowrap;
        }

        th {
            background: #667eea;
            color: white;
        }

        /* Status indicator */
        .status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 1000;
        }

        /* Debug info */
        .debug-info {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="status">Horizontal Scrollbar Test</div>
    
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="sidebar">
                <h3>Navigation</h3>
                <p>Dashboard</p>
                <p>User Roles</p>
                <p>Settings</p>
            </div>

            <!-- Main content -->
            <main class="user-role-container">
                <div class="content-wrapper">
                    <div class="card">
                        <h1>User Roles & Permissions</h1>
                        <p>The horizontal scrollbar should appear below. Scroll right to see more content →</p>
                    </div>

                    <div class="card">
                        <h2>Permissions Matrix</h2>
                        <div class="table-responsive">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Resource</th>
                                        <th>Action</th>
                                        <th>Admin</th>
                                        <th>Manager</th>
                                        <th>Officer</th>
                                        <th>Reviewer</th>
                                        <th>User</th>
                                        <th>Applicant</th>
                                        <th>Corp Reviewer</th>
                                        <th>Corp Approver</th>
                                        <th>SP Officer</th>
                                        <th>SP Reviewer</th>
                                        <th>SP Read-Only</th>
                                        <th>SME Owner</th>
                                        <th>SME Director</th>
                                        <th>SME Manager</th>
                                        <th>SME Finance</th>
                                        <th>SME User</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Applications</td>
                                        <td>View</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                    </tr>
                                    <tr>
                                        <td>Applications</td>
                                        <td>Create</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✗</td>
                                        <td>✗</td>
                                        <td>✓</td>
                                        <td>✗</td>
                                        <td>✗</td>
                                        <td>✗</td>
                                        <td>✗</td>
                                        <td>✗</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✗</td>
                                        <td>✗</td>
                                    </tr>
                                    <tr>
                                        <td>Applications</td>
                                        <td>Edit</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✗</td>
                                        <td>✗</td>
                                        <td>✓</td>
                                        <td>✗</td>
                                        <td>✗</td>
                                        <td>✓</td>
                                        <td>✗</td>
                                        <td>✗</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✓</td>
                                        <td>✗</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="card">
                        <h2>Additional Content</h2>
                        <p>This content extends beyond the viewport width to demonstrate horizontal scrolling.</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <div class="debug-info">
        <div id="debug"></div>
    </div>

    <script>
        // Debug information
        function updateDebugInfo() {
            const container = document.querySelector('.user-role-container');
            const content = document.querySelector('.content-wrapper');
            const debug = document.getElementById('debug');
            
            debug.innerHTML = `
                Container Width: ${container.clientWidth}px<br>
                Content Width: ${content.scrollWidth}px<br>
                Scroll Left: ${container.scrollLeft}px<br>
                Has H-Scroll: ${container.scrollWidth > container.clientWidth ? 'YES' : 'NO'}<br>
                Scrollbar Visible: ${container.offsetHeight > container.clientHeight || container.offsetWidth > container.clientWidth ? 'YES' : 'NO'}
            `;
        }

        // Update debug info on load and scroll
        window.addEventListener('load', updateDebugInfo);
        document.querySelector('.user-role-container').addEventListener('scroll', updateDebugInfo);
        window.addEventListener('resize', updateDebugInfo);
    </script>
</body>
</html>