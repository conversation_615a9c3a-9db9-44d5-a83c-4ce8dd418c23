const mongoose = require('mongoose');

const addressSchema = new mongoose.Schema({
  street: String,
  city: String,
  province: String,
  postalCode: String,
  country: { type: String, default: 'South Africa' }
});

const bbbeeProfileSchema = new mongoose.Schema({
  bbbeeLevel: { 
    type: String, 
    enum: ['Level 1', 'Level 2', 'Level 3', 'Level 4', 'Level 5', 'Level 6', 'Level 7', 'Level 8', 'Non-Compliant'] 
  },
  bbbeeCertificateExpiryDate: Date,
  blackOwnershipPercentage: { type: Number, min: 0, max: 100 },
  blackWomenOwnershipPercentage: { type: Number, min: 0, max: 100 },
  blackYouthOwnershipPercentage: { type: Number, min: 0, max: 100 },
  blackPeopleWithDisabilityOwnershipPercentage: { type: Number, min: 0, max: 100 },
  bbbeeCategory: String,
  isExxaroSupplier: { type: Boolean, default: false },
  sdEd: String,
  hasLatestInvoice: { type: Boolean, default: false },
  financialYearEnd: Date,
  lastAnnualTurnover: Number,
  hasAnnualFinancialStatements: { type: Boolean, default: false }
});

const smeEntitySchema = new mongoose.Schema({
  id: {
    type: String,
    unique: true
  },
  
  // Business Registration Information
  businessRegistration: {
    legalName: { type: String, required: true },
    tradingName: { type: String, required: true },
    registrationNumber: String,
    cipcRegistrationNumber: { type: String, required: true },
    entityType: { 
      type: String,
      enum: ['Pty Ltd', 'Close Corporation', 'Non-Profit', 'Partnership', 'Sole Proprietor', 'Other'],
      required: true 
    },
    startTradingDate: { type: Date, required: true },
    cipcRegistrationDocument: String, // File path
    vatNumber: String,
    taxNumber: String,
    industry: String,
    businessType: String,
    yearEstablished: Number,
    employeeCount: Number,
    website: String
  },
  
  // Primary Contact Information
  primaryContact: {
    name: { type: String, required: true },
    surname: { type: String, required: true },
    email: { type: String, required: true, match: /.+\@.+\..+/ },
    mainOfficeNumber: String,
    cellphoneNumber: { type: String, required: true },
    position: String
  },
  
  // Business Address
  businessAddress: addressSchema,
  
  // Financial Profile
  financialProfile: {
    annualTurnover: Number,
    netProfit: Number,
    currentAssets: Number,
    currentLiabilities: Number,
    totalDebt: Number,
    lastFinancialYear: Date,
    taxClearanceExpiryDate: Date,
    taxClearanceCertificateDocument: String, // File path
    vatRegistered: { type: Boolean, default: false },
    vatRegistrationNumber: String,
    vatCertificateDocument: String // File path
  },
  
  // BBBEE Profile
  bbbeeProfile: bbbeeProfileSchema,
  
  // Programme Registrations
  programmeRegistrations: [{
    programmeId: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: 'FundingProgramme',
      required: true
    },
    registrationDate: { type: Date, default: Date.now },
    status: { 
      type: String, 
      enum: ['active', 'inactive', 'suspended'], 
      default: 'active' 
    },
    eligibilityStatus: {
      type: String,
      enum: ['eligible', 'ineligible', 'pending_review'],
      default: 'pending_review'
    },
    notes: String
  }],
  
  // Document Management
  documents: [{
    type: { 
      type: String, 
      enum: [
        'cipc_certificate', 
        'tax_clearance', 
        'vat_certificate', 
        'bbbee_certificate',
        'financial_statements',
        'bank_statements',
        'other'
      ],
      required: true
    },
    filename: { type: String, required: true },
    originalName: String,
    uploadDate: { type: Date, default: Date.now },
    uploadedBy: String, // User ID who uploaded
    status: { 
      type: String, 
      enum: ['pending', 'verified', 'rejected'], 
      default: 'pending' 
    },
    verificationNotes: String,
    verifiedBy: String,
    verificationDate: Date
  }],
  
  // Status and Metadata
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'under_review', 'pending_verification'],
    default: 'pending_verification'
  },
  
  // Verification Status
  verificationStatus: {
    businessRegistration: { type: Boolean, default: false },
    contactDetails: { type: Boolean, default: false },
    financialInformation: { type: Boolean, default: false },
    bbbeeCompliance: { type: Boolean, default: false },
    overallStatus: { 
      type: String, 
      enum: ['pending', 'partial', 'complete'], 
      default: 'pending' 
    }
  },
  
  // Audit Fields
  createdBy: String,
  createdAt: { type: Date, default: Date.now },
  updatedBy: String,
  updatedAt: { type: Date, default: Date.now },
  
  // Change History
  changeHistory: [{
    field: String,
    oldValue: mongoose.Schema.Types.Mixed,
    newValue: mongoose.Schema.Types.Mixed,
    changedBy: String,
    changedAt: { type: Date, default: Date.now },
    reason: String
  }]
});

// Pre-save middleware to generate SME ID
smeEntitySchema.pre('save', async function(next) {
  if (!this.id) {
    const count = await this.constructor.countDocuments();
    const nextNumber = (count + 1).toString().padStart(3, '0');
    this.id = `SME-2025-${nextNumber}`;
  }
  
  // Update verification status
  this.verificationStatus.overallStatus = this.calculateOverallVerificationStatus();
  
  this.updatedAt = Date.now();
  next();
});

// Method to calculate overall verification status
smeEntitySchema.methods.calculateOverallVerificationStatus = function() {
  const statuses = [
    this.verificationStatus.businessRegistration,
    this.verificationStatus.contactDetails,
    this.verificationStatus.financialInformation,
    this.verificationStatus.bbbeeCompliance
  ];
  
  const verifiedCount = statuses.filter(status => status === true).length;
  
  if (verifiedCount === 0) return 'pending';
  if (verifiedCount === statuses.length) return 'complete';
  return 'partial';
};

// Indexes for performance
smeEntitySchema.index({ 'businessRegistration.cipcRegistrationNumber': 1 });
smeEntitySchema.index({ 'businessRegistration.legalName': 'text', 'businessRegistration.tradingName': 'text' });
smeEntitySchema.index({ 'primaryContact.email': 1 });
smeEntitySchema.index({ status: 1 });
smeEntitySchema.index({ 'programmeRegistrations.programmeId': 1 });

const SMEEntity = mongoose.model('SMEEntity', smeEntitySchema);

module.exports = SMEEntity;
