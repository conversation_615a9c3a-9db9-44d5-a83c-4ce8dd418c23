# SME Entity Implementation - Complete Guide

This document provides a comprehensive guide for the SME Entity implementation in the funding screening portal. The implementation transforms the system from an application-centric model to an SME-centric model, enabling better data management and multi-programme support.

## Table of Contents

1. [Overview](#overview)
2. [Architecture Changes](#architecture-changes)
3. [Implementation Components](#implementation-components)
4. [Migration Process](#migration-process)
5. [API Documentation](#api-documentation)
6. [Testing Guide](#testing-guide)
7. [Deployment Instructions](#deployment-instructions)
8. [Troubleshooting](#troubleshooting)

## Overview

### What Changed

The system has been restructured to support SMEs (Small and Medium Enterprises) as first-class entities:

- **Before**: Business information was embedded in each application
- **After**: SME entities exist independently and applications reference them
- **Benefits**: Eliminates data duplication, enables multi-programme participation, provides SME self-service capabilities

### Key Features

- ✅ **SME Entity Management**: Centralized SME business profiles
- ✅ **SME User System**: Self-service portal for SME users
- ✅ **Multi-Programme Registration**: SMEs can register for multiple programmes
- ✅ **Data Migration**: Automated migration from embedded to entity model
- ✅ **Backward Compatibility**: Existing workflows remain functional
- ✅ **RESTful APIs**: Comprehensive API endpoints with proper error handling

## Architecture Changes

### Database Schema Changes

#### New Models

1. **SMEEntity** (`backend/src/models/sme-entity.js`)
   - Business registration information
   - Primary contact details
   - Financial profile
   - BBBEE compliance data
   - Programme registrations
   - Document management
   - Verification status

2. **SMEUser** (`backend/src/models/sme-user.js`)
   - Authentication credentials
   - Personal information
   - SME entity association
   - Role-based permissions
   - Account security features

#### Updated Models

3. **Application** (`backend/src/models/application.js`)
   - Added `smeEntityId` reference
   - Removed embedded business data schemas
   - Added application-specific document management
   - Enhanced audit trail support

### API Architecture

#### New Endpoints

- **SME Entities**: `/api/v1/sme-entities`
- **SME Users**: `/api/v1/sme-users`

#### Authentication & Authorization

- Enhanced middleware for SME user support
- Permission-based access control
- Entity-specific data filtering

## Implementation Components

### 1. Models (`backend/src/models/`)

#### SME Entity Model
```javascript
// Key features:
- Unique SME ID generation (SME-2025-001)
- Business registration validation
- Programme registration tracking
- Document verification workflow
- Change history audit trail
```

#### SME User Model
```javascript
// Key features:
- Secure password hashing
- Account lockout protection
- Email verification
- Role-based permissions
- Login attempt tracking
```

### 2. Routes (`backend/src/routes/`)

#### SME Entity Routes (`sme-entities.js`)
- `GET /` - List SME entities (paginated, searchable)
- `POST /` - Create new SME entity
- `GET /:id` - Get SME entity details
- `PUT /:id` - Update SME entity
- `GET /:id/applications` - Get entity applications
- `POST /:id/programme-registrations` - Register for programme
- `GET /:id/users` - Get entity users

#### SME User Routes (`sme-users.js`)
- `POST /register` - SME user registration
- `POST /login` - SME user authentication
- `GET /profile` - Get current user profile
- `PUT /profile` - Update user profile
- `POST /change-password` - Change password
- `POST /verify-email` - Email verification

### 3. Middleware (`backend/src/middleware/auth.js`)

#### Enhanced Authentication
- `checkSMEAccess` - SME entity access control
- `addSMEEntityFilter` - Data filtering for SME users
- `checkSMEPermission` - Permission validation

### 4. Migration System (`backend/src/migrations/`)

#### Migration Script (`migrate-to-sme-entities.js`)
- Groups applications by unique business entities
- Creates SME entities from application data
- Generates SME users for each entity
- Updates applications to reference entities
- Comprehensive validation and error handling

#### Rollback Script (`rollback-sme-migration.js`)
- Restores embedded business data
- Removes SME entities and users
- Provides safety mechanism for migration issues

## Migration Process

### Prerequisites

1. **Backup Database**: Always backup before migration
2. **Test Environment**: Run migration in test environment first
3. **Downtime Planning**: Plan for brief system downtime

### Step-by-Step Migration

#### 1. Dry Run (Recommended)
```bash
node backend/src/scripts/run-sme-migration.js --dry-run
```

#### 2. Actual Migration
```bash
node backend/src/scripts/run-sme-migration.js
```

#### 3. Validation
```bash
node backend/src/scripts/run-sme-migration.js --validate
```

#### 4. Rollback (if needed)
```bash
node backend/src/scripts/run-sme-migration.js --rollback
```

### Migration Process Details

1. **Data Analysis**: Groups applications by unique business entities
2. **Entity Creation**: Creates SME entities from business data
3. **User Generation**: Creates primary SME users with temporary passwords
4. **Application Updates**: Updates applications to reference SME entities
5. **Data Cleanup**: Removes embedded business data from applications
6. **Validation**: Verifies data integrity and migration success

## API Documentation

### Authentication

All SME API endpoints require authentication via JWT token:

```http
Authorization: Bearer <jwt-token>
```

### Response Format

All API responses follow a consistent format:

```json
{
  "success": true,
  "data": { ... },
  "meta": {
    "timestamp": "2025-01-28T10:00:00Z",
    "version": "1.0"
  }
}
```

### Error Handling

Errors follow standardized format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": { ... }
  }
}
```

### SME Entity Endpoints

#### List SME Entities
```http
GET /api/v1/sme-entities?page=1&limit=25&status=active&search=company
```

#### Create SME Entity
```http
POST /api/v1/sme-entities
Content-Type: application/json

{
  "businessRegistration": {
    "legalName": "Example Business Pty Ltd",
    "tradingName": "Example Business",
    "cipcRegistrationNumber": "2023/123456/07",
    "entityType": "Pty Ltd",
    "startTradingDate": "2023-01-01"
  },
  "primaryContact": {
    "name": "John",
    "surname": "Doe",
    "email": "<EMAIL>",
    "cellphoneNumber": "+***********"
  }
}
```

### SME User Endpoints

#### Register SME User
```http
POST /api/v1/sme-users/register
Content-Type: application/json

{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "smeEntityId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "smeRole": "owner"
}
```

#### SME User Login
```http
POST /api/v1/sme-users/login
Content-Type: application/json

{
  "username": "john_doe",
  "password": "SecurePassword123!"
}
```

## Testing Guide

### Unit Tests

Test the following components:

1. **Model Validation**
   ```javascript
   // Test SME entity creation
   // Test SME user authentication
   // Test application referencing
   ```

2. **API Endpoints**
   ```javascript
   // Test CRUD operations
   // Test authentication flows
   // Test error handling
   ```

3. **Migration Scripts**
   ```javascript
   // Test data transformation
   // Test rollback functionality
   // Test validation logic
   ```

### Integration Tests

1. **End-to-End Workflows**
   - SME registration → User creation → Application submission
   - Multi-programme registration
   - Data migration validation

2. **Security Testing**
   - Authentication bypass attempts
   - Authorization boundary testing
   - Input validation testing

### Performance Tests

1. **Database Performance**
   - Query optimization validation
   - Index effectiveness
   - Large dataset handling

2. **API Performance**
   - Response time benchmarks
   - Concurrent user handling
   - Rate limiting validation

## Deployment Instructions

### 1. Pre-Deployment

1. **Code Review**: Ensure all changes are reviewed
2. **Database Backup**: Create full database backup
3. **Environment Setup**: Configure environment variables
4. **Dependency Check**: Verify all npm packages are installed

### 2. Deployment Steps

1. **Deploy Code**
   ```bash
   git pull origin main
   npm install
   ```

2. **Run Migration**
   ```bash
   # Test migration first
   node backend/src/scripts/run-sme-migration.js --dry-run
   
   # Run actual migration
   node backend/src/scripts/run-sme-migration.js
   ```

3. **Restart Services**
   ```bash
   pm2 restart all
   # or
   npm run start
   ```

4. **Validate Deployment**
   ```bash
   # Check API health
   curl http://localhost:3002/health
   
   # Validate migration
   node backend/src/scripts/run-sme-migration.js --validate
   ```

### 3. Post-Deployment

1. **Monitor Logs**: Check for any errors or warnings
2. **Test Key Workflows**: Verify critical functionality works
3. **User Communication**: Notify users of new features
4. **Documentation Update**: Update user documentation

## Troubleshooting

### Common Issues

#### 1. Migration Fails

**Symptoms**: Migration script exits with errors

**Solutions**:
- Check database connectivity
- Verify required fields in existing data
- Run with `--dry-run` to identify issues
- Check application logs for detailed errors

#### 2. Authentication Issues

**Symptoms**: SME users cannot log in

**Solutions**:
- Verify JWT secret configuration
- Check user account status
- Validate email verification status
- Check for account lockouts

#### 3. Permission Errors

**Symptoms**: Access denied errors for SME users

**Solutions**:
- Verify user permissions in database
- Check SME entity associations
- Validate middleware configuration
- Review role assignments

#### 4. Data Inconsistency

**Symptoms**: Missing or incorrect data after migration

**Solutions**:
- Run validation script
- Check migration logs
- Verify data mapping logic
- Consider rollback if critical

### Debugging Tools

#### 1. Migration Validation
```bash
node backend/src/scripts/run-sme-migration.js --validate
```

#### 2. Database Queries
```javascript
// Check SME entities
db.smeentities.find().count()

// Check SME users
db.smeusers.find().count()

// Check applications with SME references
db.applications.find({smeEntityId: {$exists: true}}).count()
```

#### 3. API Testing
```bash
# Test SME entity endpoint
curl -H "Authorization: Bearer <token>" \
     http://localhost:3002/api/v1/sme-entities

# Test SME user login
curl -X POST \
     -H "Content-Type: application/json" \
     -d '{"username":"test","password":"test"}' \
     http://localhost:3002/api/v1/sme-users/login
```

### Support Contacts

For technical issues or questions:

1. **Development Team**: [<EMAIL>]
2. **System Administrator**: [<EMAIL>]
3. **Project Manager**: [<EMAIL>]

### Rollback Procedure

If critical issues arise:

1. **Immediate Rollback**
   ```bash
   node backend/src/scripts/run-sme-migration.js --rollback
   ```

2. **Code Rollback**
   ```bash
   git checkout <previous-commit>
   pm2 restart all
   ```

3. **Database Restore** (if needed)
   ```bash
   mongorestore --drop <backup-directory>
   ```

---

## Conclusion

The SME Entity implementation provides a robust foundation for managing SME data and enabling self-service capabilities. The migration process is designed to be safe and reversible, with comprehensive validation and error handling.

For questions or issues, please refer to the troubleshooting section or contact the development team.

## Recent Updates & Enhancements

### January 28, 2025 - Version 1.1

#### New Application Creation Workflow
- **Enhanced "+New Application" functionality**: Applications can now only be created for existing SME entities
- **Hierarchical selection process**: Corporate Sponsor → Programme → SME Entity selection flow
- **Pre-populated forms**: When creating applications from SME Management, all fields are automatically filled
- **Validation enforcement**: System prevents application creation without proper SME entity association

#### SME Management Portal Enhancements
- **Modern UI redesign**: Complete visual overhaul with gradient backgrounds and glass morphism effects
- **Enhanced "+Create New SME" workflow**: 
  - Modal-based selection requiring Corporate Sponsor and Programme selection first
  - Hierarchical filtering system ensuring SMEs are created within proper programme structure
  - Visual feedback with information cards showing selected sponsor and programme details
  - Form validation preventing creation without proper selections

#### SME Dashboard Improvements
- **Enhanced vertical scrollbar**: Always visible scrollbar with professional blue gradient styling
- **Forced content height**: Guaranteed scrollable content with 150vh minimum height
- **Cross-browser compatibility**: Custom scrollbar styling for both Firefox and Webkit browsers
- **Interactive states**: Hover and active effects with smooth transitions

#### Frontend Component Updates

##### SME Management Component (`frontend/src/app/components/admin/sme-management/`)
- **Modal integration**: Added corporate sponsor and programme selection modal
- **Service integration**: Integrated `FundingProgrammeService` and `CorporateSponsorService`
- **Enhanced navigation**: Passes sponsor and programme context to SME registration
- **Professional styling**: Modern gradient backgrounds, glass morphism effects, enhanced buttons and badges

##### SME Registration Component (`frontend/src/app/components/sme-register/`)
- **Admin creation support**: Handles `adminCreate`, `corporateSponsorId`, and `programmeId` query parameters
- **Authentication bypass**: Prevents redirect to dashboard for admin-created SMEs
- **Context preservation**: Maintains sponsor and programme information throughout registration

##### Application Creation Component (`frontend/src/app/Components/application-create/`)
- **SME entity requirement**: Applications can only be created for existing SME entities
- **Enhanced scrollbar**: Forced vertical scrollbar with professional styling
- **Pre-selection support**: Automatically populates fields when coming from SME Management

#### Technical Improvements

##### Enhanced Scrollbar Implementation
```scss
// Professional scrollbar with gradient styling
&::-webkit-scrollbar {
  width: 16px !important;
  background: #f8f9fa !important;
  display: block !important;
}

&::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2) !important;
}
```

##### Modal System Implementation
- **Glass morphism design**: Semi-transparent modals with backdrop blur effects
- **Responsive layout**: Mobile-friendly modal design with proper breakpoints
- **Form validation**: Real-time validation with error messaging
- **Loading states**: Visual feedback during data loading operations

#### Business Logic Enhancements

##### Application Creation Requirements
- **SME entity validation**: All applications must be associated with existing SME entities
- **Programme structure enforcement**: SMEs must be created within specific programme contexts
- **Data integrity**: Proper relationships maintained between sponsors, programmes, and SMEs

##### Admin Workflow Improvements
- **Structured SME creation**: Step-by-step process ensuring proper data hierarchy
- **Visual guidance**: Clear information cards and progress indicators
- **Error prevention**: Validation at each step prevents incomplete or invalid data entry

#### UI/UX Improvements

##### Modern Design System
- **Gradient backgrounds**: Beautiful purple gradients throughout the interface
- **Glass morphism effects**: Semi-transparent cards with backdrop blur
- **Enhanced typography**: Improved font hierarchy and spacing
- **Interactive animations**: Smooth hover effects and transitions

##### Professional Styling
- **Badge system**: Gradient badges with proper shadows and rounded corners
- **Button enhancements**: Modern button styling with hover animations
- **Table improvements**: Enhanced table styling with sticky headers and hover effects
- **Card design**: Rounded corners, enhanced shadows, and better content organization

#### Security & Validation
- **Enhanced form validation**: Comprehensive client-side and server-side validation
- **Data integrity checks**: Proper validation of sponsor-programme-SME relationships
- **Authentication improvements**: Better handling of admin vs. SME user contexts

#### Performance Optimizations
- **Efficient scrollbar rendering**: Optimized CSS for better performance
- **Lazy loading**: Improved component loading strategies
- **Memory management**: Better cleanup of event listeners and subscriptions

### Breaking Changes
- **Application creation**: Applications can no longer be created without existing SME entities
- **SME creation workflow**: "+Create New SME" now requires sponsor and programme selection

### Migration Notes
- Existing applications remain functional with backward compatibility
- New application creation follows enhanced workflow
- SME entities created through admin panel automatically include programme associations

---

**Last Updated**: January 28, 2025  
**Version**: 1.1  
**Status**: Enhanced Implementation Complete
