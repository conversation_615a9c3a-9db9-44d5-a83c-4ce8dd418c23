const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const swaggerUi = require('swagger-ui-express');
const YAML = require('yamljs');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });

const app = express();
const PORT = process.env.PORT || 3002;
app.use(express.json());
// Set port to 3002 to match frontend proxy
process.env.PORT = 3002;

// Import database configuration
const dbConfig = require('./config/database-config');

// Security middleware
app.use(helmet()); // Security headers

// CORS configuration
const corsOptions = {
  origin: [
    process.env.FRONTEND_URL || 'http://localhost:4200',
    'http://localhost:4200',
    'https://app.domain.com',
    'http://localhost:3000'  // Add development server origin
  ],
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

// Request logging
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// General rate limiting
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: {
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests, please try again later.'
    }
  }
});

app.use(generalLimiter);

// API Prefix
const API_PREFIX = '/api/v1'; // Ensure this matches your .env configuration

// Load Swagger documentation
const swaggerDocument = YAML.load(path.join(__dirname, '../swagger.yaml'));

// Swagger UI setup with custom options
const swaggerOptions = {
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Funding Screening App API Documentation',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    filter: true,
    tryItOutEnabled: true
  }
};

async function initializeServer() {
  try {
    // Connect to the appropriate database based on the current mode
    const dbConnection = await dbConfig.connectToDatabase();
    
    if (!dbConnection.success) {
      console.error('Failed to connect to database:', dbConnection.message);
      process.exit(1);
    }
    
    console.log('Connected to MongoDB database');
    
    // Load the database modules but don't seed any data
    // This prevents auto-generation of applications on restart
    console.log('Loading database modules without seeding data...');
    
    // Load questionnaire data modules
    const seedQuestionnaireData = require('./seed-questionnaire-data');
    
    // Initialize authentication data
    const { seedAuthData } = require('./seed-auth-data');
    console.log('Initializing authentication system...');
    const authSeedResult = await seedAuthData();
    if (authSeedResult) {
      console.log('Authentication system initialized successfully');
    } else {
      console.warn('Authentication system initialization had issues, but continuing...');
    }
    
    console.log('Database modules loaded successfully');
    
    // Health check endpoint
    app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: 'funding-screening-app-backend',
        version: '1.0.0'
      });
    });

    // Swagger API Documentation
    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument, swaggerOptions));

    // Redirect root to API documentation
    app.get('/', (req, res) => {
      res.redirect('/api-docs');
    });

    // Import routes
    const authRoutes = require('./routes/auth');
    const adminRoutes = require('./routes/admin');
    const applicationRoutes = require('./routes/applications');
    const publicApplicationRoutes = require('./routes/public-applications');
    const siteVisitRoutes = require('./routes/site-visits');
    const interviewRoutes = require('./routes/interviews');
    const schedulerRoutes = require('./routes/scheduler');
    // Comment out the questionnaire routes to prevent the error
    // const questionnaireRoutes = require('../questionnaire-routes');
    const corporateSponsorRoutes = require('./routes/corporate-sponsors');
    const fundingProgrammeRoutes = require('./routes/funding-programmes');
    const serviceProviderRoutes = require('./routes/service-providers');
    const reportsRoutes = require('./routes/reports');
    const dashboardsRoutes = require('./routes/dashboards');
    const processAnalyticsRoutes = require('./routes/process-analytics');
    const scorecardRoutes = require('./routes/scorecards');
    const scorecardTemplateRoutes = require('./routes/scorecard-templates');
    
    // Import notification routes
    const notificationRoutes = require('./routes/notifications');
    const adminNotificationRoutes = require('./routes/admin-notifications');
    
    // Import chat routes
    const chatRoutes = require('./routes/chat');
    
    // Import workflow routes
    const workflowRoutes = require('./routes/workflows');
    
    // Import loan management routes
    const loanManagementRoutes = require('./routes/loan-management');

    // Import stage status
    const stagestatus = require('./routes/stage-status');
    
    // Import SME routes
    const smeEntityRoutes = require('./routes/sme-entities');
    const smeUserRoutes = require('./routes/sme-users');

    // Mount authentication routes first
    app.use(`${API_PREFIX}/auth`, authRoutes);
    app.use(`${API_PREFIX}/admin`, adminRoutes);

    // Mount other routes under the API prefix
    app.use(`${API_PREFIX}/applications`, applicationRoutes);
    app.use(`${API_PREFIX}/public-applications`, publicApplicationRoutes);
    app.use(`${API_PREFIX}/site-visits`, siteVisitRoutes);
    app.use(`${API_PREFIX}/interviews`, interviewRoutes);
    app.use(`${API_PREFIX}/scheduler`, schedulerRoutes);
    app.use(`${API_PREFIX}/corporate-sponsors`, corporateSponsorRoutes);
    app.use(`${API_PREFIX}/funding-programmes`, fundingProgrammeRoutes);
    app.use(`${API_PREFIX}/service-providers`, serviceProviderRoutes);
    app.use(`${API_PREFIX}/reports`, reportsRoutes);
    app.use(`${API_PREFIX}/analytics`, dashboardsRoutes);
    app.use(`${API_PREFIX}/process-analytics`, processAnalyticsRoutes);
    app.use(`${API_PREFIX}/scorecards`, scorecardRoutes);
    app.use(`${API_PREFIX}/scorecard-templates`, scorecardTemplateRoutes);
    
    // Mount notification routes
    app.use(`${API_PREFIX}/notifications`, notificationRoutes);
    app.use(`${API_PREFIX}/admin/notifications`, adminNotificationRoutes);
    
    // Mount chat routes
    app.use(`${API_PREFIX}/chat`, chatRoutes);
    
    // Mount workflow routes
    app.use(`${API_PREFIX}/workflows`, workflowRoutes);
    
    // Mount loan management routes
    app.use(`${API_PREFIX}/loan-management`, loanManagementRoutes);
    // Stages Status
    app.use(`${API_PREFIX}/stage-status`, stagestatus);
    
    // Mount SME routes
    app.use(`${API_PREFIX}/sme-entities`, smeEntityRoutes);
    app.use(`${API_PREFIX}/sme-users`, smeUserRoutes);

    // Global error handler
    app.use((error, req, res, next) => {
      console.error('Global error handler:', error);
      
      // Handle JWT errors
      if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          error: {
            code: 'INVALID_TOKEN',
            message: 'Invalid token'
          }
        });
      }
      
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          error: {
            code: 'TOKEN_EXPIRED',
            message: 'Token has expired'
          }
        });
      }

      // Handle validation errors
      if (error.name === 'ValidationError') {
        return res.status(400).json({
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation error',
            details: Object.values(error.errors).map(err => err.message)
          }
        });
      }

      // Default error response
      res.status(error.status || 500).json({
        error: {
          code: error.code || 'SERVER_ERROR',
          message: error.message || 'Internal server error',
          timestamp: new Date().toISOString(),
          path: req.path
        }
      });
    });

    // 404 handler
    app.use('*', (req, res) => {
      res.status(404).json({
        error: {
          code: 'NOT_FOUND',
          message: 'Route not found',
          path: req.originalUrl
        }
      });
    });

    // Start the server after mounting routes
    const server = app.listen(PORT, () => {
      console.log(`✅ Server running on port ${PORT}`);
      console.log(`🌐 API available at: http://localhost:${PORT}/api/v1`);
      console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
    });

    // Initialize WebSocket service (temporarily disabled to fix login issue)
    try {
      const WebSocketService = require('./services/websocket-service');
      WebSocketService.initialize(server);
      WebSocketService.startHeartbeat();
    } catch (error) {
      console.warn('WebSocket service failed to initialize:', error.message);
      console.log('Continuing without WebSocket service...');
    }

    // Start notification scheduler (process scheduled notifications every minute)
    const NotificationService = require('./services/notification-service');
    setInterval(async () => {
      try {
        await NotificationService.processScheduledNotifications();
      } catch (error) {
        console.error('Error processing scheduled notifications:', error);
      }
    }, 60000); // 1 minute

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM received, shutting down gracefully');
      WebSocketService.shutdown();
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('SIGINT received, shutting down gracefully');
      WebSocketService.shutdown();
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });
  } catch (error) {
    console.error('Error initializing server:', error);
    process.exit(1);
  }
}

initializeServer();
