<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Roles Table - Horizontal Scroll Test</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }

        /* Container structure matching the Angular component */
        .container-fluid {
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        .row {
            height: 100%;
            display: flex;
            flex-wrap: nowrap;
        }

        /* Sidebar simulation */
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            flex-shrink: 0;
        }

        /* Main content area with horizontal scroll */
        .user-role-container {
            flex: 1;
            height: 100%;
            overflow-x: auto !important;
            overflow-y: auto !important;
            padding: 2rem;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            position: relative;
        }

        /* Force horizontal scrollbar to always be visible */
        .user-role-container::-webkit-scrollbar {
            height: 16px !important;
            width: 16px !important;
        }

        .user-role-container::-webkit-scrollbar-track {
            background: #f1f1f1 !important;
            border-radius: 8px !important;
            border: 1px solid #ddd;
        }

        .user-role-container::-webkit-scrollbar-thumb {
            background: #888 !important;
            border-radius: 8px !important;
            border: 2px solid #f1f1f1;
        }

        .user-role-container::-webkit-scrollbar-thumb:hover {
            background: #555 !important;
        }

        .user-role-container::-webkit-scrollbar-corner {
            background: #f1f1f1 !important;
        }

        /* Firefox scrollbar */
        .user-role-container {
            scrollbar-width: auto !important;
            scrollbar-color: #888 #f1f1f1 !important;
        }

        /* Content wrapper to ensure minimum width */
        .content-wrapper {
            min-width: 1400px;
        }

        /* Card styles */
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            padding: 2rem;
        }

        /* Table styles */
        .table-responsive {
            overflow-x: visible !important;
            overflow-y: visible !important;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.9);
        }

        table {
            width: 100%;
            min-width: 1600px;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tr:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        /* Header styles */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2.5rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .page-header h1 {
            font-size: 2.8rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* Badge styles */
        .badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.8rem;
        }

        .badge-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .badge-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .badge-warning {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
            color: #2d3436;
        }

        .badge-info {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        /* Success/Error indicators */
        .text-success {
            color: #28a745;
        }

        .text-danger {
            color: #dc3545;
        }

        /* Scroll indicator */
        .scroll-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="sidebar">
                <h3>Navigation</h3>
                <p>Dashboard</p>
                <p>Applications</p>
                <p>User Roles</p>
                <p>Settings</p>
            </div>

            <!-- Main content with horizontal scroll -->
            <main class="user-role-container">
                <div class="content-wrapper">
                    <div class="page-header">
                        <h1>User Roles & Permissions</h1>
                        <p>Scroll horizontally to see the entire permissions matrix →</p>
                    </div>

                    <!-- Permissions Matrix -->
                    <div class="card">
                        <h2>Permissions Matrix</h2>
                        <p>This table shows the permissions assigned to each user role. The horizontal scrollbar below allows you to view all columns.</p>
                        
                        <div class="table-responsive">
                            <table>
                                <thead>
                                    <tr>
                                        <th rowspan="2">Resource</th>
                                        <th rowspan="2">Action</th>
                                        <th colspan="6">20/20Insight</th>
                                        <th colspan="2">CorporateSponsor</th>
                                        <th colspan="3">ServiceProvider</th>
                                        <th colspan="5">SME</th>
                                    </tr>
                                    <tr>
                                        <th>Admin</th>
                                        <th>Manager</th>
                                        <th>Officer</th>
                                        <th>Reviewer</th>
                                        <th>User</th>
                                        <th>Applicant</th>
                                        <th>Reviewer</th>
                                        <th>Approver</th>
                                        <th>Officer</th>
                                        <th>Reviewer</th>
                                        <th>Read-Only</th>
                                        <th>Owner</th>
                                        <th>Director</th>
                                        <th>Manager</th>
                                        <th>Finance</th>
                                        <th>User</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td rowspan="6"><strong>Applications</strong></td>
                                        <td>View</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                    </tr>
                                    <tr>
                                        <td>Create</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                    </tr>
                                    <tr>
                                        <td>Edit</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                    </tr>
                                    <tr>
                                        <td>Approve</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                    </tr>
                                    <tr>
                                        <td>Delete</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-danger">✗</td>
                                    </tr>
                                    <tr>
                                        <td>Export</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-success">✓</td>
                                        <td class="text-danger">✗</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Additional content to demonstrate vertical scrolling -->
                    <div class="card">
                        <h2>Role Overview</h2>
                        <p>The system supports different user roles based on organization type:</p>
                        <ul>
                            <li><span class="badge badge-primary">20/20Insight</span> - Internal organization managing the funding screening system</li>
                            <li><span class="badge badge-danger">CorporateSponsor</span> - External organizations providing funding for programmes</li>
                            <li><span class="badge badge-warning">ServiceProvider</span> - Third-party organizations providing specialized services</li>
                            <li><span class="badge badge-info">SME</span> - Small Medium Enterprises applying for funding</li>
                        </ul>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Scroll indicator -->
    <div class="scroll-indicator">
        Horizontal scroll is enabled. Use the scrollbar at the bottom.
    </div>
</body>
</html>