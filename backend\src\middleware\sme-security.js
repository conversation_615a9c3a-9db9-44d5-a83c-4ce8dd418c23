const mongoose = require('mongoose');

/**
 * SME Entity Ownership Validation Middleware
 * Ensures SME users can only access their own SME entity data
 */
const validateSMEEntityOwnership = async (req, res, next) => {
  if (!req.user || req.user.organizationType !== 'SME') {
    return next(); // Not an SME user, skip validation
  }

  const requestedEntityId = req.params.id || req.body.smeEntityId;
  const userSMEEntityId = req.user.smeEntityId;

  if (!userSMEEntityId) {
    return res.status(403).json({
      error: {
        code: 'MISSING_SME_ENTITY_ASSOCIATION',
        message: 'SME user must have SME entity association'
      }
    });
  }

  if (requestedEntityId && requestedEntityId !== userSMEEntityId.toString()) {
    console.warn(`SME user ${req.user.id} attempted to access entity ${requestedEntityId} but owns ${userSMEEntityId}`);
    return res.status(403).json({
      error: {
        code: 'SME_ENTITY_ACCESS_DENIED',
        message: 'Access denied: You can only access your own SME entity'
      }
    });
  }

  next();
};

/**
 * Check SME Entity Access Middleware
 * Applies SME entity ownership validation for SME users
 */
const checkSMEEntityAccess = (req, res, next) => {
  if (req.user && req.user.organizationType === 'SME') {
    return validateSMEEntityOwnership(req, res, next);
  }
  next();
};

/**
 * SME Entity Association Validation Middleware
 * Validates that SME users have proper entity associations
 */
const validateSMEEntityAssociation = async (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ 
      error: { code: 'UNAUTHORIZED', message: 'User not authenticated' }
    });
  }

  const userRoles = req.user.roles || [];
  const organizationType = req.user.organizationType;
  
  try {
    // SME users MUST have SME entity association
    if (organizationType === 'SME' || userRoles.some(role => ['SME_OWNER', 'SME_MANAGER', 'SME_EMPLOYEE', 'SME_APPLICANT'].includes(role))) {
      if (!req.user.smeEntityId) {
        throw new Error('SME user missing SME entity association');
      }
      
      // Validate that the SME entity exists and user is authorized
      const SMEEntity = require('../models/sme-entity');
      const smeEntity = await SMEEntity.findById(req.user.smeEntityId);
      
      if (!smeEntity) {
        throw new Error('SME user associated with non-existent SME entity');
      }
      
      // Check if user is authorized for this SME entity
      const isAuthorizedUser = smeEntity.users.some(user => 
        user.userId.toString() === req.user.id.toString()
      );
      
      if (!isAuthorizedUser) {
        throw new Error('SME user not authorized for associated SME entity');
      }
      
      // Validate SME entity status
      if (smeEntity.status === 'inactive' || smeEntity.status === 'suspended') {
        throw new Error(`SME entity is ${smeEntity.status} - access denied`);
      }
    }
    
    next();
  } catch (error) {
    console.error('SME entity association validation failed:', error.message);
    return res.status(403).json({
      error: {
        code: 'SME_ENTITY_ASSOCIATION_ERROR',
        message: error.message
      }
    });
  }
};

/**
 * SME Application Filter Middleware
 * Filters applications to show only those belonging to the SME user's entity
 */
const addSMEApplicationFilter = (req, res, next) => {
  if (!req.user) {
    return next();
  }

  // Initialize entity filter if not exists
  if (!req.entityFilter) {
    req.entityFilter = {};
  }

  const userRoles = req.user.roles || [];
  const organizationType = req.user.organizationType;

  // Admin users - no filtering (can see all applications)
  if (userRoles.includes('admin') || userRoles.includes('System Administrator')) {
    return next();
  }

  // SME users - filter by their SME entity
  if (organizationType === 'SME') {
    const smeEntityId = req.user.smeEntityId;
    if (!smeEntityId) {
      return res.status(403).json({
        error: {
          code: 'MISSING_SME_ENTITY_ASSOCIATION',
          message: 'SME user must have SME entity association to access applications'
        }
      });
    }
    
    // Filter applications by SME entity ownership
    req.entityFilter.smeEntityId = smeEntityId;
    
    console.log(`SME Application Filter Applied: User ${req.user.id} can only see applications for SME entity ${smeEntityId}`);
  }

  next();
};

module.exports = {
  validateSMEEntityOwnership,
  checkSMEEntityAccess,
  validateSMEEntityAssociation,
  addSMEApplicationFilter
};
