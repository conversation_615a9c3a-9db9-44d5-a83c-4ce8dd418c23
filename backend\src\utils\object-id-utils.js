const mongoose = require('mongoose');

/**
 * Normalize ObjectId to ensure consistent handling across all routes
 * @param {string|ObjectId} id - The ID to normalize
 * @returns {ObjectId|string|null} Normalized ObjectId or null if invalid
 */
const normalizeObjectId = (id) => {
  if (!id) return null;
  
  // Handle different input types
  if (typeof id === 'object' && id._id) {
    id = id._id;
  }
  
  if (mongoose.Types.ObjectId.isValid(id)) {
    return new mongoose.Types.ObjectId(id);
  }
  
  // Log warning for invalid ObjectIds
  console.warn(`Invalid ObjectId format: ${id}`);
  return id; // Keep as string for fallback comparison
};

/**
 * Normalize array of ObjectIds
 * @param {Array} ids - Array of IDs to normalize
 * @returns {Array} Array of normalized ObjectIds
 */
const normalizeObjectIdArray = (ids) => {
  if (!Array.isArray(ids)) return [];
  return ids.map(id => normalizeObjectId(id)).filter(id => id !== null);
};

module.exports = {
  normalizeObjectId,
  normalizeObjectIdArray
};
