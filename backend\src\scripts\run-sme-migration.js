#!/usr/bin/env node

/**
 * SME Entity Migration Script
 * 
 * This script migrates existing application data to the new SME entity structure.
 * It should be run after deploying the new SME entity models and before using the new system.
 * 
 * Usage:
 *   node backend/src/scripts/run-sme-migration.js
 * 
 * Options:
 *   --dry-run    : Run migration in dry-run mode (no actual changes)
 *   --rollback   : Rollback the migration (restore embedded data)
 *   --validate   : Only run validation checks
 */

const mongoose = require('mongoose');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Import database configuration
const dbConfig = require('../config/database-config');

// Import migration functions
const { migrateToSMEEntities, validateMigration } = require('../migrations/migrate-to-sme-entities');
const { rollbackSMEMigration } = require('../migrations/rollback-sme-migration');

// Parse command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const isRollback = args.includes('--rollback');
const isValidateOnly = args.includes('--validate');

async function runMigration() {
  console.log('=== SME Entity Migration Script ===');
  console.log(`Mode: ${isDryRun ? 'DRY RUN' : 'LIVE'}`);
  console.log(`Action: ${isRollback ? 'ROLLBACK' : isValidateOnly ? 'VALIDATE' : 'MIGRATE'}`);
  console.log('=====================================\n');

  try {
    // Connect to database
    console.log('Connecting to database...');
    const dbConnection = await dbConfig.connectToDatabase();
    
    if (!dbConnection.success) {
      console.error('Failed to connect to database:', dbConnection.message);
      process.exit(1);
    }
    
    console.log('✓ Connected to MongoDB database\n');

    // Load models to ensure they're registered
    require('../models/index');
    console.log('✓ Models loaded\n');

    if (isValidateOnly) {
      // Only run validation
      console.log('Running validation checks...\n');
      await validateMigration();
      console.log('\n✓ Validation completed');
      
    } else if (isRollback) {
      // Run rollback
      if (isDryRun) {
        console.log('DRY RUN: Would rollback SME entity migration');
        console.log('This would restore embedded business data in applications');
        console.log('and remove all SME entities and users.');
      } else {
        console.log('Starting rollback process...\n');
        await rollbackSMEMigration();
        console.log('\n✓ Rollback completed successfully');
      }
      
    } else {
      // Run migration
      if (isDryRun) {
        console.log('DRY RUN: Would migrate applications to SME entity structure');
        console.log('This would create SME entities from existing application data');
        console.log('and update applications to reference these entities.');
        
        // Show what would be migrated
        const Application = require('../models/application');
        const appCount = await Application.countDocuments();
        console.log(`\nFound ${appCount} applications that would be processed`);
        
        if (appCount > 0) {
          const sampleApp = await Application.findOne().lean();
          if (sampleApp && sampleApp.businessInfo) {
            console.log('\nSample business info that would be extracted:');
            console.log(`- Legal Name: ${sampleApp.businessInfo.legalName}`);
            console.log(`- Trading Name: ${sampleApp.businessInfo.tradingName}`);
            console.log(`- CIPC Number: ${sampleApp.businessInfo.cipcRegistrationNumber}`);
            console.log(`- Entity Type: ${sampleApp.businessInfo.entityType}`);
          }
        }
      } else {
        console.log('Starting migration process...\n');
        await migrateToSMEEntities();
        console.log('\n✓ Migration completed successfully');
      }
    }

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('\n✓ Database connection closed');
  }
}

// Show help if requested
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
SME Entity Migration Script

This script migrates existing application data to the new SME entity structure.

Usage:
  node backend/src/scripts/run-sme-migration.js [options]

Options:
  --dry-run     Run migration in dry-run mode (no actual changes)
  --rollback    Rollback the migration (restore embedded data)
  --validate    Only run validation checks
  --help, -h    Show this help message

Examples:
  # Run migration in dry-run mode first
  node backend/src/scripts/run-sme-migration.js --dry-run

  # Run actual migration
  node backend/src/scripts/run-sme-migration.js

  # Validate migration results
  node backend/src/scripts/run-sme-migration.js --validate

  # Rollback migration if needed
  node backend/src/scripts/run-sme-migration.js --rollback

Important Notes:
  - Always run with --dry-run first to see what will be changed
  - Backup your database before running the actual migration
  - The migration creates SME entities from existing application data
  - Applications will be updated to reference SME entities instead of embedding data
  - SME users will be created for each unique business entity
`);
  process.exit(0);
}

// Confirm before running destructive operations
if (!isDryRun && !isValidateOnly) {
  console.log('⚠️  WARNING: This will make changes to your database!');
  console.log('Make sure you have backed up your data before proceeding.');
  console.log('');
  
  // In a real implementation, you might want to add a confirmation prompt
  // For now, we'll just show the warning
  setTimeout(() => {
    runMigration();
  }, 2000);
} else {
  runMigration();
}
