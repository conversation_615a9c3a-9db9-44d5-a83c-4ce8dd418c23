const mongoose = require('mongoose');
const Application = require('../models/application');
const SMEEntity = require('../models/sme-entity');
const SMEUser = require('../models/sme-user');

const rollbackSMEMigration = async () => {
  console.log('=== Starting SME Migration Rollback ===');
  
  try {
    // Step 1: Get all applications with SME entity references
    const applications = await Application.find({ smeEntityId: { $exists: true } })
      .populate('smeEntityId')
      .lean();
    
    console.log(`Found ${applications.length} applications to rollback`);
    
    // Step 2: Restore embedded business data
    let restoredCount = 0;
    
    for (const app of applications) {
      if (!app.smeEntityId) {
        console.warn(`Application ${app.id} has no SME entity reference`);
        continue;
      }
      
      const smeEntity = app.smeEntityId;
      
      try {
        await Application.findByIdAndUpdate(app._id, {
          personalInfo: {
            firstName: smeEntity.primaryContact.name,
            lastName: smeEntity.primaryContact.surname,
            email: smeEntity.primaryContact.email,
            phone: smeEntity.primaryContact.cellphoneNumber,
            address: smeEntity.businessAddress
          },
          businessInfo: {
            ...smeEntity.businessRegistration,
            address: smeEntity.businessAddress
          },
          contactDetails: smeEntity.primaryContact,
          financialInfo: smeEntity.financialProfile,
          bbbeeProfile: smeEntity.bbbeeProfile,
          $unset: { smeEntityId: 1 }
        });
        
        restoredCount++;
      } catch (error) {
        console.error(`Error restoring application ${app.id}:`, error.message);
      }
    }
    
    console.log(`Successfully restored ${restoredCount} applications`);
    
    // Step 3: Remove SME entities and users (with confirmation)
    console.log('WARNING: About to delete all SME entities and users');
    console.log('This action cannot be undone. Proceeding in 5 seconds...');
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const deletedUsers = await SMEUser.deleteMany({});
    console.log(`Deleted ${deletedUsers.deletedCount} SME users`);
    
    const deletedEntities = await SMEEntity.deleteMany({});
    console.log(`Deleted ${deletedEntities.deletedCount} SME entities`);
    
    console.log('=== SME Migration Rollback Completed ===');
    
  } catch (error) {
    console.error('Rollback failed:', error);
    throw error;
  }
};

module.exports = { rollbackSMEMigration };
