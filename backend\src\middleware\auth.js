/**
 * Authentication and authorization middleware
 */

const jwt = require('jsonwebtoken');
const User = require('../models/user');
const SMEUser = require('../models/sme-user');

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

/**
 * Middleware to authenticate JWT token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const authenticateToken = (req, res, next) => {
  console.log('🔍 AUTHENTICATE TOKEN - START');
  console.log('   - Route:', req.path);
  console.log('   - Method:', req.method);
  
  const authHeader = req.headers.authorization;
  console.log('   - Auth header exists:', !!authHeader);
  console.log('   - Auth header:', authHeader ? `${authHeader.substring(0, 20)}...` : 'null');

  if (!authHeader) {
    console.log('❌ AUTHENTICATE TOKEN - Missing auth header, returning 401');
    return res.status(401).json({
      error: {
        code: 'MISSING_AUTH_HEADER',
        message: 'Authorization header is required'
      }
    });
  }

  const token = authHeader.split(' ')[1];
  console.log('   - Token exists:', !!token);
  console.log('   - Token preview:', token ? `${token.substring(0, 20)}...` : 'null');

  if (!token) {
    console.log('❌ AUTHENTICATE TOKEN - Invalid auth format, returning 401');
    return res.status(401).json({
      error: {
        code: 'INVALID_AUTH_FORMAT',
        message: 'Invalid authorization format. Expected "Bearer TOKEN"'
      }
    });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
    console.log('✅ AUTHENTICATE TOKEN - SUCCESS');
    console.log('   - User ID:', decoded.id);
    console.log('   - User roles:', decoded.roles);
    next();
  } catch (error) {
    console.log('❌ AUTHENTICATE TOKEN - JWT verification failed:', error.name, error.message);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: {
          code: 'TOKEN_EXPIRED',
          message: 'Token has expired'
        }
      });
    }
    
    return res.status(401).json({
      error: {
        code: 'INVALID_TOKEN',
        message: 'Invalid token'
      }
    });
  }
};

/**
 * Middleware to check user roles
 * @param {Array} roles - Array of allowed roles
 * @returns {Function} Middleware function
 */
const authorizeRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    // Check both legacy role field and new roles array
    const userRoles = req.user.roles || [];
    const legacyRole = req.user.role;
    
    const hasRole = userRoles.some(role => roles.includes(role)) ||
                   (legacyRole && roles.includes(legacyRole));
    
    if (!hasRole) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_ROLES',
          message: 'User does not have the required roles to access this resource'
        }
      });
    }

    next();
  };
};

/**
 * Middleware to authorize based on user roles (plural version)
 * @param {Array} roles - Array of allowed roles
 * @returns {Function} Middleware function
 */
const authorizeRoles = (roles) => {
  return authorizeRole(roles);
};

/**
 * Middleware to check user permissions
 * @param {Array} requiredPermissions - Array of required permissions
 * @returns {Function} Middleware function
 */
const checkPermission = (requiredPermissions) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    const userPermissions = req.user.permissions || [];
    const hasPermission = requiredPermissions.every(permission =>
      userPermissions.includes(permission)
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'User does not have the required permissions to access this resource'
        }
      });
    }

    next();
  };
};

/**
 * Optional authentication middleware - doesn't fail if no token provided
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const optionalAuth = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader) {
    return next();
  }

  const token = authHeader.split(' ')[1];
  
  if (!token) {
    return next();
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
  } catch (error) {
    // Ignore token errors for optional auth
    console.log('Optional auth token error:', error.message);
  }
  
  next();
};

/**
 * Middleware to add entity filtering to queries for entity-specific users
 * Enhanced to support multiple organization types and better filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const addEntityFilter = (req, res, next) => {
  if (!req.user) {
    return next();
  }

  // Initialize entity filter object
  req.entityFilter = {};

  console.log('Auth Middleware: Adding entity filter for user:', {
    id: req.user.id,
    email: req.user.email,
    roles: req.user.roles,
    organizationType: req.user.organizationType,
    organizationId: req.user.organizationId,
    corporateSponsorId: req.user.corporateSponsorId,
    fundingProgrammeId: req.user.fundingProgrammeId
  });

  // Check if user is entity-specific
  const userRoles = req.user.roles || [];
  const organizationType = req.user.organizationType;
  
  // Enhanced role-based filtering
  const isCorporateSponsorUser = userRoles.includes('corporate-sponsor-user') || 
                                organizationType === 'CorporateSponsor';
  const isProgrammeUser = userRoles.includes('programme-user') || 
                         organizationType === 'ServiceProvider';
  const isAdmin = userRoles.includes('admin') || userRoles.includes('manager') || 
                 userRoles.includes('System Administrator') || userRoles.includes('Manager');

  // Admin users can see all data - no filtering
  if (isAdmin) {
    console.log('Auth Middleware: User has admin privileges, no entity filtering applied');
    return next();
  }

  // Corporate sponsor users - filter by their organization
  if (isCorporateSponsorUser) {
    const sponsorId = req.user.corporateSponsorId || req.user.organizationId;
    if (sponsorId) {
      // Handle both string and ObjectId formats
      const mongoose = require('mongoose');
      if (mongoose.Types.ObjectId.isValid(sponsorId)) {
        req.entityFilter.corporateSponsorId = new mongoose.Types.ObjectId(sponsorId);
      } else {
        req.entityFilter.corporateSponsorId = sponsorId;
      }
      console.log(`Auth Middleware: Applied corporate sponsor filter: ${sponsorId}`);
    } else {
      console.warn('Auth Middleware: Corporate sponsor user has no organization ID');
    }
  }

  // Programme/Service provider users - filter by their assigned programmes
  if (isProgrammeUser) {
    const programmeId = req.user.fundingProgrammeId || req.user.organizationId;
    if (programmeId) {
      // Handle both string and ObjectId formats
      const mongoose = require('mongoose');
      if (mongoose.Types.ObjectId.isValid(programmeId)) {
        req.entityFilter.programmeId = new mongoose.Types.ObjectId(programmeId);
      } else {
        req.entityFilter.programmeId = programmeId;
      }
      console.log(`Auth Middleware: Applied programme filter: ${programmeId}`);
    } else if (req.user.programmeAssignments && req.user.programmeAssignments.length > 0) {
      // Handle multiple programme assignments
      const assignedProgrammeIds = req.user.programmeAssignments.map(assignment => {
        const id = assignment.programmeId;
        const mongoose = require('mongoose');
        if (mongoose.Types.ObjectId.isValid(id)) {
          return new mongoose.Types.ObjectId(id);
        }
        return id;
      });
      req.entityFilter.programmeId = { $in: assignedProgrammeIds };
      console.log(`Auth Middleware: Applied multiple programme filter: ${assignedProgrammeIds}`);
    } else {
      console.warn('Auth Middleware: Programme user has no programme assignments');
    }
  }

  // SME/Applicant users - filter by their own applications
  if (organizationType === 'SME' || userRoles.includes('applicant')) {
    req.entityFilter.applicantId = req.user.id;
    console.log(`Auth Middleware: Applied applicant filter: ${req.user.id}`);
  }

  console.log('Auth Middleware: Final entity filter:', req.entityFilter);
  next();
};

/**
 * Middleware to check entity-specific access for applications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const checkEntityAccess = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      error: {
        code: 'UNAUTHORIZED',
        message: 'User not authenticated'
      }
    });
  }

  const userRoles = req.user.roles || [];
  const isAdmin = userRoles.includes('admin') || userRoles.includes('manager');
  
  // Admin and manager users have access to all entities
  if (isAdmin) {
    return next();
  }

  const isCorporateSponsorUser = userRoles.includes('corporate-sponsor-user');
  const isProgrammeUser = userRoles.includes('programme-user');

  // If user is not entity-specific, allow access (for backward compatibility)
  if (!isCorporateSponsorUser && !isProgrammeUser) {
    return next();
  }

  // Entity-specific users must have their entity associations
  if (isCorporateSponsorUser && !req.user.corporateSponsorId) {
    return res.status(403).json({
      error: {
        code: 'MISSING_ENTITY_ASSOCIATION',
        message: 'Corporate sponsor user must have a corporate sponsor association'
      }
    });
  }

  if (isProgrammeUser && !req.user.fundingProgrammeId) {
    return res.status(403).json({
      error: {
        code: 'MISSING_ENTITY_ASSOCIATION',
        message: 'Programme user must have a funding programme association'
      }
    });
  }

  next();
};

/**
 * Middleware to check SME entity access
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const checkSMEAccess = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: 'User not authenticated'
      }
    });
  }

  // System users (admin, managers, etc.) have access to all SME entities
  if (req.user.userType !== 'sme_user') {
    return next();
  }

  // SME users can only access their own entity
  const requestedEntityId = req.params.id || req.params.smeEntityId;
  if (requestedEntityId && req.user.smeEntityId !== requestedEntityId) {
    return res.status(403).json({
      success: false,
      error: {
        code: 'FORBIDDEN',
        message: 'Access denied to this SME entity'
      }
    });
  }

  next();
};

/**
 * Enhanced entity filter to include SME users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const addSMEEntityFilter = (req, res, next) => {
  if (!req.user) {
    return next();
  }

  // Initialize entity filter object if not exists
  if (!req.entityFilter) {
    req.entityFilter = {};
  }

  console.log('SME Auth Middleware: Adding SME entity filter for user:', {
    id: req.user.userId,
    userType: req.user.userType,
    smeEntityId: req.user.smeEntityId,
    permissions: req.user.permissions
  });

  // SME users can only see their own entity's data
  if (req.user.userType === 'sme_user' && req.user.smeEntityId) {
    const mongoose = require('mongoose');
    if (mongoose.Types.ObjectId.isValid(req.user.smeEntityId)) {
      req.entityFilter.smeEntityId = new mongoose.Types.ObjectId(req.user.smeEntityId);
    } else {
      req.entityFilter.smeEntityId = req.user.smeEntityId;
    }
    console.log(`SME Auth Middleware: Applied SME entity filter: ${req.user.smeEntityId}`);
  }

  console.log('SME Auth Middleware: Final entity filter:', req.entityFilter);
  next();
};

/**
 * Middleware to check SME user permissions
 * @param {Array} requiredPermissions - Array of required permissions
 * @returns {Function} Middleware function
 */
const checkSMEPermission = (requiredPermissions) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    // System users bypass SME permission checks
    if (req.user.userType !== 'sme_user') {
      return next();
    }

    const userPermissions = req.user.permissions || [];
    const hasPermission = requiredPermissions.every(permission =>
      userPermissions.includes(permission)
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: `Required permissions: ${requiredPermissions.join(', ')}`,
          details: {
            required: requiredPermissions,
            provided: userPermissions
          }
        }
      });
    }

    next();
  };
};

module.exports = {
  authenticateToken,
  authorizeRole,
  authorizeRoles,
  checkPermission,
  optionalAuth,
  addEntityFilter,
  checkEntityAccess,
  checkSMEAccess,
  addSMEEntityFilter,
  checkSMEPermission
};
