/**
 * Node.js script to update user organization types
 * 
 * Run this script from the backend directory:
 * node scripts/update-organization-types.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import the User model
const User = require('../src/models/user');

async function updateOrganizationTypes() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/screening_portal');
    console.log('Connected to MongoDB');

    // Get all users first
    const allUsers = await User.find({}, 'email organizationType roles');
    console.log('\n📋 Current user organization types:');
    allUsers.forEach(user => {
      console.log(`  ${user.email}: ${user.organizationType} (roles: ${user.roles.join(', ')})`);
    });

    console.log('\n🔄 Updating organization types based on roles...');

    // Update users with corporate roles
    const corporateUpdate = await User.updateMany(
      { roles: { $in: ["corporate-sponsor-user", "CORPORATE_REVIEWER", "CORPORATE_APPROVER"] } },
      { $set: { organizationType: "CorporateSponsor" } }
    );
    console.log(`  ✅ Updated ${corporateUpdate.modifiedCount} users to CorporateSponsor`);

    // Update users with service provider roles
    const serviceProviderUpdate = await User.updateMany(
      { roles: { $in: ["SERVICE_PROVIDER_LOAN_OFFICER", "SERVICE_PROVIDER_REVIEWER", "SERVICE_PROVIDER_READ_ONLY", "programme-user"] } },
      { $set: { organizationType: "ServiceProvider" } }
    );
    console.log(`  ✅ Updated ${serviceProviderUpdate.modifiedCount} users to ServiceProvider`);

    // Update SME/applicant users
    const smeUpdate = await User.updateMany(
      { roles: { $in: ["SME_OWNER", "SME_MANAGER", "SME_EMPLOYEE", "SME_APPLICANT", "applicant"] } },
      { $set: { organizationType: "SmallMediumEnterprise" } }
    );
    console.log(`  ✅ Updated ${smeUpdate.modifiedCount} users to SmallMediumEnterprise`);

    // Keep admin users as 20/20Insight (they should already be this by default)
    const adminUpdate = await User.updateMany(
      { roles: { $in: ["admin", "manager", "System Administrator", "Manager"] } },
      { $set: { organizationType: "20/20Insight" } }
    );
    console.log(`  ✅ Confirmed ${adminUpdate.matchedCount} admin users as 20/20Insight`);

    // Show updated results
    const updatedUsers = await User.find({}, 'email organizationType roles');
    console.log('\n📋 Updated user organization types:');
    updatedUsers.forEach(user => {
      console.log(`  ${user.email}: ${user.organizationType} (roles: ${user.roles.join(', ')})`);
    });

    console.log('\n🎉 Organization types updated successfully!');
    console.log('\n💡 Now you can test the sidebar filtering:');
    console.log('   1. Log in with different users');
    console.log('   2. Check browser console for filtering debug output');
    console.log('   3. Verify that Messages section only shows for 20/20Insight users');

  } catch (error) {
    console.error('❌ Error updating organization types:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
updateOrganizationTypes();
