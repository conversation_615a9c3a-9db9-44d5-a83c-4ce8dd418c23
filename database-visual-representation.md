# Database Visual Representation - Screening Portal

## Overview
This document provides a comprehensive visual representation of the database schema for the Screening Portal system. The system uses MongoDB with Mongoose ODM and consists of multiple interconnected entities.

## Entity Relationship Diagram (ERD)

```mermaid
erDiagram
    %% Core Entities
    CorporateSponsor ||--o{ FundingProgramme : "sponsors"
    CorporateSponsor ||--o{ User : "has employees"
    CorporateSponsor ||--o{ Application : "receives"
    
    FundingProgramme ||--o{ Application : "has"
    FundingProgramme ||--o{ CommitteeMeeting : "schedules"
    FundingProgramme ||--o{ User : "assigns"
    FundingProgramme ||--o{ ProgrammeAssignment : "has"
    
    User ||--o{ Application : "owns/manages"
    User ||--o{ Report : "creates"
    User ||--o{ ChatMessage : "sends"
    User }o--o{ ChatRoom : "participates in"
    User ||--o{ Notification : "receives"
    
    Application ||--o{ ApprovalWorkflow : "follows"
    Application ||--o{ SiteVisit : "requires"
    Application ||--o{ Scorecard : "evaluated by"
    Application ||--o{ Interview : "has"
    Application ||--o{ LoanOffer : "receives"
    
    %% SME Entities
    SMEEntity ||--o{ SMEUser : "has users"
    SMEEntity ||--o{ Application : "submits"
    
    %% Communication Entities
    ChatRoom ||--o{ ChatMessage : "contains"
    ChatRoom ||--o{ ChatParticipant : "has"
    ChatParticipant }o--|| User : "is"
    
    %% Financial Entities
    LoanProduct ||--o{ LoanOffer : "basis for"
    LoanOffer ||--|| Loan : "becomes"
    Loan ||--o{ Payment : "has"
    
    %% Supporting Entities
    ServiceProvider ||--o{ User : "has employees"
    Role ||--o{ Permission : "has"
    User }o--o{ Role : "assigned"
    
    %% Workflow Entities
    Workflow ||--o{ WorkflowAssignment : "assigned to"
    NotificationTemplate ||--o{ Notification : "generates"
    Report ||--o{ ScheduledReport : "scheduled as"
    Dashboard ||--o{ Widget : "contains"

    %% Define Entities with Key Attributes
    CorporateSponsor {
        ObjectId _id PK
        string name
        string description
        string logo
        object contactPerson
        object address
        string website
        string industry
        number totalFunding
        number activePrograms
        string status
        date createdAt
        date updatedAt
    }
    
    FundingProgramme {
        ObjectId _id PK
        string name
        string description
        ObjectId corporateSponsorId FK
        array objectives
        array fundingCriteria
        array fundingTypes
        object budget
        object timeline
        string status
        ObjectId createdBy FK
        date createdAt
        date updatedAt
    }
    
    User {
        ObjectId _id PK
        string id UK
        string username UK
        string email UK
        string password
        string firstName
        string lastName
        string role
        array roles
        array permissions
        string organizationType
        ObjectId corporateSponsorId FK
        ObjectId fundingProgrammeId FK
        string status
        boolean isActive
        date lastLogin
        date createdAt
        date updatedAt
    }
    
    Application {
        ObjectId _id PK
        string id UK
        ObjectId programmeId FK
        ObjectId corporateSponsorId FK
        string registrationNumber
        number fundingAmount
        date submissionDate
        string currentMainStage
        string currentSubStage
        string currentStatus
        object personalInfo
        object businessInfo
        object contactDetails
        object financialInfo
        object bbbeeProfile
        array documents
        number score
        string assignedTo
        date lastUpdated
    }
    
    SMEEntity {
        ObjectId _id PK
        string entityId UK
        string name
        string registrationNumber UK
        string taxNumber
        object contactDetails
        object businessDetails
        object bankingDetails
        string status
        date createdAt
        date updatedAt
    }
    
    SMEUser {
        ObjectId _id PK
        string userId UK
        ObjectId smeEntityId FK
        string email UK
        string password
        object personalInfo
        string role
        array permissions
        string status
        date lastLogin
        date createdAt
        date updatedAt
    }
    
    ChatRoom {
        ObjectId _id PK
        string id UK
        string name
        string description
        string type
        array directMessageUsers
        object relatedEntity
        object settings
        boolean isActive
        ObjectId createdBy FK
        date lastActivity
        ObjectId lastMessage FK
        number messageCount
    }
    
    ChatMessage {
        ObjectId _id PK
        string id UK
        ObjectId roomId FK
        ObjectId senderId FK
        object content
        array attachments
        object voiceNote
        object metadata
        ObjectId replyTo FK
        array mentions
        array readBy
        object deliveryStatus
        array reactions
        date createdAt
    }
    
    LoanOffer {
        ObjectId _id PK
        string offerId UK
        string applicationId FK
        string productId FK
        number offeredAmount
        number approvedAmount
        number interestRate
        number term
        string termUnit
        object fees
        array repaymentSchedule
        object riskAssessment
        string status
        date validUntil
        date createdAt
    }
    
    Loan {
        ObjectId _id PK
        string loanNumber UK
        string offerId FK
        string applicationId FK
        ObjectId borrowerId FK
        string productId FK
        number principalAmount
        number disbursedAmount
        number interestRate
        number term
        string status
        date disbursementDate
        number totalOutstanding
        array payments
        object collateral
        array guarantors
    }
```

## Database Tables Structure

### 1. Core Business Entities

#### CorporateSponsor Collection
```
┌─────────────────────────────────────────────────────────────┐
│                      CorporateSponsor                        │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ name: String (required)                                      │
│ description: String                                          │
│ logo: String                                                 │
│ contactPerson: {                                            │
│   name: String                                              │
│   email: String                                             │
│   phone: String                                             │
│   position: String                                          │
│ }                                                           │
│ address: {                                                  │
│   street: String                                            │
│   city: String                                              │
│   province: String                                          │
│   postalCode: String                                        │
│   country: String                                           │
│ }                                                           │
│ website: String                                             │
│ industry: String                                            │
│ totalFunding: Number                                        │
│ activePrograms: Number                                      │
│ totalBeneficiaries: Number                                  │
│ status: Enum ['active', 'inactive']                        │
│ createdAt: Date                                             │
│ updatedAt: Date                                             │
└─────────────────────────────────────────────────────────────┘
```

#### FundingProgramme Collection
```
┌─────────────────────────────────────────────────────────────┐
│                      FundingProgramme                        │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ name: String (required)                                      │
│ description: String                                          │
│ corporateSponsorId: ObjectId → CorporateSponsor (FK)        │
│ objectives: [String]                                         │
│ fundingCriteria: [String]                                    │
│ fundingTypes: [Enum]                                        │
│ eligibilityCriteria: [String]                               │
│ geographicalCoverage: {                                     │
│   provinces: [String]                                       │
│   municipalities: [String]                                  │
│ }                                                           │
│ sectors: {                                                  │
│   included: [String]                                        │
│   excluded: [String]                                        │
│ }                                                           │
│ budget: {                                                   │
│   totalAmount: Number                                       │
│   currency: String                                          │
│   allocated: Number                                         │
│   remaining: Number                                         │
│ }                                                           │
│ timeline: {                                                 │
│   startDate: Date                                           │
│   endDate: Date                                             │
│   applicationDeadline: Date                                 │
│ }                                                           │
│ status: Enum ['draft','active','paused','completed']        │
│ createdBy: ObjectId → User (FK)                             │
│ createdAt: Date                                             │
│ updatedAt: Date                                             │
└─────────────────────────────────────────────────────────────┘
```

#### Application Collection
```
┌─────────────────────────────────────────────────────────────┐
│                        Application                           │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ id: String (UK, auto: APP-2025-XXX)                        │
│ programmeId: ObjectId → FundingProgramme (FK)               │
│ corporateSponsorId: ObjectId → CorporateSponsor (FK)        │
│ registrationNumber: String                                   │
│ fundingAmount: Number                                        │
│ submissionDate: Date                                         │
│ currentMainStage: Enum                                      │
│ currentSubStage: Enum                                       │
│ currentStatus: Enum                                         │
│ owner: String                                               │
│ personalInfo: {                                             │
│   firstName: String                                         │
│   lastName: String                                          │
│   idNumber: String                                          │
│   dateOfBirth: Date                                         │
│   gender: String                                            │
│   nationality: String                                       │
│ }                                                           │
│ businessInfo: {                                             │
│   businessName: String                                      │
│   registrationNumber: String                                │
│   industry: String                                          │
│   yearsInOperation: Number                                  │
│   businessType: String                                      │
│ }                                                           │
│ contactDetails: {                                           │
│   email: String                                             │
│   phone: String                                             │
│   address: Object                                           │
│ }                                                           │
│ financialInfo: {                                            │
│   annualTurnover: Number                                    │
│   employees: Number                                         │
│   bankAccount: Object                                       │
│ }                                                           │
│ bbbeeProfile: {                                             │
│   level: String                                             │
│   blackOwnership: Number                                    │
│   blackManagement: Number                                   │
│ }                                                           │
│ documents: [String]                                         │
│ notes: [String]                                             │
│ tags: [String]                                              │
│ stageStatusAuditLog: [Object]                              │
│ score: Number                                               │
│ assignedTo: String                                          │
│ approvalWorkflow: Object                                    │
│ lastUpdated: Date                                           │
└─────────────────────────────────────────────────────────────┘
```

### 2. User Management Entities

#### User Collection
```
┌─────────────────────────────────────────────────────────────┐
│                           User                               │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ id: String (UK, sparse)                                     │
│ username: String (UK, required)                             │
│ email: String (UK, required)                                │
│ password: String (hashed, required)                         │
│ firstName: String                                           │
│ lastName: String                                            │
│ role: Enum ['admin','analyst','manager',etc]               │
│ roles: [String] (multi-role support)                       │
│ permissions: [String]                                       │
│ organizationType: Enum                                      │
│ organizationId: String                                      │
│ corporateSponsorId: ObjectId → CorporateSponsor (FK)        │
│ fundingProgrammeId: ObjectId → FundingProgramme (FK)        │
│ programmeAssignments: [{                                    │
│   programmeId: ObjectId                                     │
│   role: String                                              │
│ }]                                                          │
│ phone: String                                               │
│ profilePicture: String                                      │
│ status: Enum ['active','inactive','suspended']             │
│ isActive: Boolean                                           │
│ lastLogin: Date                                             │
│ resetPasswordToken: String                                  │
│ resetPasswordExpires: Date                                  │
│ createdAt: Date                                             │
│ updatedAt: Date                                             │
└─────────────────────────────────────────────────────────────┘
```

#### SMEEntity Collection
```
┌─────────────────────────────────────────────────────────────┐
│                         SMEEntity                            │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ entityId: String (UK, auto: SME-2025-XXXXX)                │
│ name: String (required)                                      │
│ registrationNumber: String (UK)                             │
│ taxNumber: String                                           │
│ contactDetails: {                                           │
│   email: String                                             │
│   phone: String                                             │
│   alternativePhone: String                                  │
│   website: String                                           │
│ }                                                           │
│ address: {                                                  │
│   street: String                                            │
│   suburb: String                                            │
│   city: String                                              │
│   province: String                                          │
│   postalCode: String                                        │
│   country: String                                           │
│ }                                                           │
│ businessDetails: {                                          │
│   industry: String                                          │
│   sector: String                                            │
│   yearsInOperation: Number                                  │
│   numberOfEmployees: Number                                 │
│   annualTurnover: Number                                    │
│   businessType: String                                      │
│ }                                                           │
│ bbbeeDetails: {                                             │
│   level: String                                             │
│   certificateNumber: String                                 │
│   expiryDate: Date                                          │
│   blackOwnership: Number                                    │
│   blackManagement: Number                                   │
│ }                                                           │
│ bankingDetails: {                                           │
│   bankName: String                                          │
│   accountNumber: String                                     │
│   branchCode: String                                        │
│   accountType: String                                       │
│ }                                                           │
│ documents: [{                                               │
│   type: String                                              │
│   fileName: String                                          │
│   fileUrl: String                                           │
│   uploadedAt: Date                                          │
│ }]                                                          │
│ status: Enum ['pending','active','suspended','inactive']    │
│ verificationStatus: Enum                                     │
│ verifiedAt: Date                                            │
│ verifiedBy: ObjectId → User                                 │
│ createdAt: Date                                             │
│ updatedAt: Date                                             │
└─────────────────────────────────────────────────────────────┘
```

#### SMEUser Collection
```
┌─────────────────────────────────────────────────────────────┐
│                          SMEUser                             │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ userId: String (UK, auto: SMEU-2025-XXXXX)                 │
│ smeEntityId: ObjectId → SMEEntity (FK, required)            │
│ email: String (UK, required)                                │
│ password: String (hashed, required)                         │
│ personalInfo: {                                             │
│   firstName: String                                         │
│   lastName: String                                          │
│   idNumber: String                                          │
│   phoneNumber: String                                       │
│   position: String                                          │
│ }                                                           │
│ role: Enum ['owner','admin','user']                        │
│ permissions: [{                                             │
│   resource: String                                          │
│   actions: [String]                                         │
│ }]                                                          │
│ status: Enum ['pending','active','suspended','inactive']    │
│ emailVerified: Boolean                                      │
│ emailVerificationToken: String                              │
│ emailVerificationExpires: Date                              │
│ passwordResetToken: String                                  │
│ passwordResetExpires: Date                                  │
│ lastLogin: Date                                             │
│ loginAttempts: Number                                       │
│ lockUntil: Date                                             │
│ createdAt: Date                                             │
│ updatedAt: Date                                             │
└─────────────────────────────────────────────────────────────┘
```

### 3. Communication Entities

#### ChatRoom Collection
```
┌─────────────────────────────────────────────────────────────┐
│                         ChatRoom                             │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ id: String (UK, auto: ROOM-2025-XXXXXX)                    │
│ name: String (required)                                      │
│ description: String                                          │
│ type: Enum ['DIRECT','GROUP','COMMITTEE',etc]              │
│ directMessageUsers: [ObjectId] → User                       │
│ relatedEntity: {                                            │
│   entityType: Enum                                          │
│   entityId: String                                          │
│   entityName: String                                        │
│ }                                                           │
│ settings: {                                                 │
│   allowFileSharing: Boolean                                 │
│   allowVoiceNotes: Boolean                                  │
│   messageRetentionDays: Number                              │
│   notifyOnMessage: Boolean                                  │
│   allowedFileTypes: [String]                               │
│   maxFileSize: Number                                       │
│ }                                                           │
│ isActive: Boolean                                           │
│ isArchived: Boolean                                         │
│ createdBy: ObjectId → User (FK)                             │
│ lastActivity: Date                                          │
│ lastMessage: ObjectId → ChatMessage                         │
│ messageCount: Number                                        │
│ createdAt: Date                                             │
│ updatedAt: Date                                             │
└─────────────────────────────────────────────────────────────┘
```

#### ChatMessage Collection
```
┌─────────────────────────────────────────────────────────────┐
│                        ChatMessage                           │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ id: String (UK, auto: MSG-2025-XXXXXXXX)                   │
│ roomId: ObjectId → ChatRoom (FK, required)                  │
│ senderId: ObjectId → User (FK, required)                    │
│ content: {                                                  │
│   text: String                                              │
│   type: Enum ['TEXT','FILE','IMAGE','VOICE',etc]          │
│ }                                                           │
│ attachments: [{                                             │
│   fileName: String                                          │
│   fileUrl: String                                           │
│   fileType: String                                          │
│   fileSize: Number                                          │
│   thumbnailUrl: String                                      │
│   uploadedAt: Date                                          │
│ }]                                                          │
│ voiceNote: {                                                │
│   duration: Number                                          │
│   fileUrl: String                                           │
│   transcription: String                                     │
│ }                                                           │
│ metadata: {                                                 │
│   isEdited: Boolean                                         │
│   editedAt: Date                                            │
│   editHistory: [Object]                                     │
│   isDeleted: Boolean                                        │
│   deletedAt: Date                                           │
│   deletedBy: ObjectId → User                                │
│ }                                                           │
│ replyTo: ObjectId → ChatMessage                             │
│ threadMessages: [ObjectId] → ChatMessage                    │
│ mentions: [{                                                │
│   userId: ObjectId → User                                   │
│   username: String                                          │
│ }]                                                          │
│ readBy: [{                                                  │
│   userId: ObjectId → User                                   │
│   readAt: Date                                              │
│ }]                                                          │
│ deliveryStatus: Object                                      │
│ reactions: [{                                               │
│   emoji: String                                             │
│   users: [Object]                                           │
│ }]                                                          │
│ systemMessage: {                                            │
│   type: Enum                                                │
│   data: Mixed                                               │
│ }                                                           │
│ createdAt: Date                                             │
│ updatedAt: Date                                             │
└─────────────────────────────────────────────────────────────┘
```

### 4. Assessment & Workflow Entities

#### Scorecard Collection
```
┌─────────────────────────────────────────────────────────────┐
│                         Scorecard                            │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ applicationId: String (required)                            │
│ stageId: String (required)                                  │
│ substageId: String (required)                               │
│ name: String                                                │
│ description: String                                         │
│ criteria: [{                                                │
│   id: String                                                │
│   name: String                                              │
│   description: String                                       │
│   weight: Number                                            │
│   maxScore: Number                                          │
│   score: Number                                             │
│   comments: String                                          │
│ }]                                                          │
│ totalScore: Number                                          │
│ maxPossibleScore: Number                                    │
│ status: Enum ['NOT_STARTED','IN_PROGRESS',etc]            │
│ isDraft: Boolean                                            │
│ currentVersion: Number                                      │
│ lastModifiedBy: String                                      │
│ lastModifiedAt: Date                                        │
│ lockedBy: String                                            │
│ lockedAt: Date                                              │
│ finalizedBy: String                                         │
│ finalizedAt: Date                                           │
└─────────────────────────────────────────────────────────────┘
```

#### Interview Collection
```
┌─────────────────────────────────────────────────────────────┐
│                         Interview                            │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ id: String (required)                                       │
│ applicationId: String (required)                            │
│ title: String (required)                                    │
│ description: String                                         │
│ agenda: String                                              │
│ templateId: String                                          │
│ templateVersion: String                                     │
│ status: Enum ['SCHEDULED','IN_PROGRESS',etc]              │
│ type: Enum ['ONLINE','IN_PERSON','PHONE']                 │
│ scheduledDate: Date (required)                              │
│ duration: Number (minutes)                                  │
│ location: String                                            │
│ meetingLink: String                                         │
│ primaryInterviewer: String (required)                       │
│ interviewee: String (required)                              │
│ questions: [{                                               │
│   id: String                                                │
│   text: String                                              │
│   type: Enum                                                │
│   required: Boolean                                         │
│   answer: String                                            │
│ }]                                                          │
│ notes: [{                                                   │
│   id: String                                                │
│   text: String                                              │
│   createdAt: Date                                           │
│   createdBy: String                                         │
│ }]                                                          │
│ participants: [{                                            │
│   id: String                                                │
│   name: String                                              │
│   role: String                                              │
│   email: String                                             │
│ }]                                                          │
│ recordings: [{                                              │
│   id: String                                                │
│   fileName: String                                          │
│   fileSize: Number                                          │
│   duration: Number                                          │
│   fileUrl: String                                           │
│   transcriptStatus: Enum                                    │
│   transcript: String                                        │
│ }]                                                          │
│ sections: [Object]                                          │
│ summary: String                                             │
│ outcome: String                                             │
│ followUpActions: String                                     │
│ completedAt: Date                                           │
│ createdAt: Date                                             │
│ updatedAt: Date                                             │
└─────────────────────────────────────────────────────────────┘
```

### 5. Financial Entities

#### LoanOffer Collection
```
┌─────────────────────────────────────────────────────────────┐
│                         LoanOffer                            │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ offerId: String (UK, required)                              │
│ applicationId: String → Application (FK, required)          │
│ productId: String → LoanProduct (FK, required)              │
│ pricingRuleId: String → PricingRule                         │
│ offeredAmount: Number                                       │
│ approvedAmount: Number                                      │
│ interestRate: Number                                        │
│ term: Number                                                │
│ termUnit: Enum ['DAYS','MONTHS','YEARS']                  │
│ repaymentFrequency: Enum                                    │
│ monthlyPayment: Number                                      │
│ totalRepayment: Number                                      │
│ totalInterest: Number                                       │
│ fees: {                                                     │
│   originationFee: Number                                    │
│   processingFee: Number                                     │
│   insuranceFee: Number                                      │
│   otherFees: [Object]                                       │
│ }                                                           │
│ collateralRequired: Boolean                                 │
│ collateralDetails: Object                                   │
│ specialConditions: [String]                                 │
│ repaymentSchedule: [{                                       │
│   paymentNumber: Number                                     │
│   dueDate: Date                                             │
│   principalAmount: Number                                   │
│   interestAmount: Number                                    │
│   totalAmount: Number                                       │
│   balance: Number                                           │
│ }]                                                          │
│ riskAssessment: {                                           │
│   riskScore: Number                                         │
│   riskCategory: String                                      │
│   factors: [Object]                                         │
│ }                                                           │
│ status: Enum ['DRAFT','PENDING','SENT',etc]               │
│ validUntil: Date                                            │
│ sentDate: Date                                              │
│ acceptedDate: Date                                          │
│ rejectedDate: Date                                          │
│ rejectionReason: String                                     │
│ approvalWorkflow: Object                                    │
│ createdAt: Date                                             │
│ updatedAt: Date                                             │
└─────────────────────────────────────────────────────────────┘
```

#### Loan Collection
```
┌─────────────────────────────────────────────────────────────┐
│                           Loan                               │
├─────────────────────────────────────────────────────────────┤
│ _id: ObjectId (PK)                                          │
│ loanNumber: String (UK, auto: LN-YYYY-XXXXXX)              │
│ offerId: String → LoanOffer (FK, required)                  │
│ applicationId: String → Application (FK, required)          │
│ borrowerId: ObjectId → User                                 │
│ productId: String → LoanProduct (FK, required)              │
│ principalAmount: Number (required)                          │
│ disbursedAmount: Number (required)                          │
│ interestRate: Number (required)                             │
│ term: Number (required)                                     │
│ termUnit: Enum ['DAYS','MONTHS','YEARS']                  │
│ status: Enum ['PENDING_DISBURSEMENT','ACTIVE',etc]        │
│ disbursementDate: Date                                      │
│ firstPaymentDate: Date                                      │
│ maturityDate: Date                                          │
│ closureDate: Date                                           │
│ outstandingPrincipal: Number                                │
│ outstandingInterest: Number                                 │
│ outstandingFees: Number                                     │
│ totalOutstanding: Number                                    │
│ totalPaidPrincipal: Number                                  │
│ totalPaidInterest: Number                                   │
│ totalPaidFees: Number                                       │
│ nextPaymentDate: Date                                       │
│ nextPaymentAmount: Number                                   │
│ daysOverdue: Number                                         │
│ payments: [{                                                │
│   paymentId: String                                         │
│   paymentNumber: Number                                     │
│   dueDate: Date                                             │
│   paymentDate: Date                                         │
│   principalAmount: Number                                   │
│   interestAmount: Number                                    │
│   feesAmount: Number                                        │
│   totalAmount: Number                                       │
│   paidAmount: Number                                        │
│   balance: Number                                           │
│   status: Enum                                              │
│   paymentMethod: String                                     │
│   transactionReference: String                              │
│   notes: String                                             │
│ }]                                                          │
│ restructureHistory: [Object]                                │
│ collateral: Object                                          │
│ guarantors: [Object]                                        │
│ documents: [Object]                                         │
│ notes: [Object]                                             │
│ performanceMetrics: Object                                  │
│ createdBy: ObjectId → User                                  │
│ updatedBy: ObjectId → User                                  │
│ createdAt: Date                                             │
│ updatedAt: Date                                             │
└─────────────────────────────────────────────────────────────┘
```

## Key Relationships and Constraints

### Primary Relationships

1. **CorporateSponsor → FundingProgramme** (1:N)
   - A corporate sponsor can have multiple funding programmes
   - Each funding programme belongs to one corporate sponsor

2. **FundingProgramme → Application** (1:N)
   - A funding programme can receive multiple applications
   - Each application is for one specific funding programme

3. **Application → Assessment Entities** (1:N)
   - One application can have multiple scorecards, interviews, site visits
   - Each assessment entity belongs to one application

4. **SMEEntity → SMEUser** (1:N)
   - An SME entity can have multiple users
   - Each SME user belongs to one SME entity

5. **User ↔ ChatRoom** (N:M via ChatParticipant)
   - Users can participate in multiple chat rooms
   - Chat rooms can have multiple participants

6. **LoanOffer → Loan** (1:1)
   - A loan offer, when accepted, becomes exactly one loan
   - Each loan originates from one loan offer

### Unique Constraints

- **User**: username, email
- **Application**: id (APP-2025-XXX)
- **SMEEntity**: entityId (SME-2025-XXXXX), registrationNumber
- **SMEUser**: userId (SMEU-2025-XXXXX), email
- **ChatRoom**: id (ROOM-2025-XXXXXX)
- **ChatMessage**: id (MSG-2025-XXXXXXXX)
- **Loan**: loanNumber (LN-YYYY-XXXXXX)

### Foreign Key Relationships

```
Application.programmeId → FundingProgramme._id
Application.corporateSponsorId → CorporateSponsor._id
User.corporateSponsorId → CorporateSponsor._id
User.fundingProgrammeId → FundingProgramme._id
SMEUser.smeEntityId → SMEEntity._id
ChatMessage.roomId → ChatRoom._id
ChatMessage.senderId → User._id
ChatParticipant.roomId → ChatRoom._id
ChatParticipant.userId → User._id
LoanOffer.applicationId → Application.id
Loan.offerId → LoanOffer.offerId
Loan.applicationId → Application.id
```

## Sample Data Visualization

### Example: Application Flow

```mermaid
graph TD
    CS[CorporateSponsor: TechCorp]
    FP[FundingProgramme: Tech Innovation Fund]
    SME[SMEEntity: InnovateTech Ltd]
    SMEU[SMEUser: <EMAIL>]
    APP[Application: APP-2025-001]
    SC[Scorecard: Initial Assessment]
    INT[Interview: Technical Review]
    SV[SiteVisit: Factory Inspection]
    LO[LoanOffer: R500,000 @ 8%]
    L[Loan: LN-2025-000001]
    
    CS -->|sponsors| FP
    SME -->|has user| SMEU
    SMEU -->|submits| APP
    APP -->|for programme| FP
    APP -->|evaluated by| SC
    APP -->|has| INT
    APP -->|requires| SV
    APP -->|receives| LO
    LO -->|becomes| L
```

### Example: Communication Structure

```mermaid
graph TD
    U1[User: Admin]
    U2[User: Analyst]
    U3[User: SME Owner]
    CR1[ChatRoom: Programme Discussion]
    CR2[ChatRoom: Application Review]
    CM1[ChatMessage: Status Update]
    CM2[ChatMessage: Document Request]
    
    U1 -->|participates in| CR1
    U2 -->|participates in| CR1
    U2 -->|participates in| CR2
    U3 -->|participates in| CR2
    CR1 -->|contains| CM1
    CR2 -->|contains| CM2
    U1 -->|sends| CM1
    U2 -->|sends| CM2
```

## Database Statistics and Indexes

### Collection Sizes (Estimated)
- **Users**: ~1,000 documents
- **Applications**: ~10,000 documents
- **ChatMessages**: ~100,000 documents
- **CorporateSponsors**: ~50 documents
- **FundingProgrammes**: ~200 documents
- **SMEEntities**: ~5,000 documents

### Key Indexes

1. **Performance Indexes**
   - `Application`: { programmeId: 1, currentStatus: 1 }
   - `ChatMessage`: { roomId: 1, createdAt: -1 }
   - `User`: { email: 1 }, { username: 1 }
   - `Notification`: { targetUsers: 1, status: 1 }

2. **Text Search Indexes**
   - `Application`: { "businessInfo.businessName": "text", "personalInfo.firstName": "text", "personalInfo.lastName": "text" }
   - `CorporateSponsor`: { name: "text", description: "text" }
   - `FundingProgramme`: { name: "text", description: "text" }

3. **Compound Indexes**
   - `ChatParticipant`: { roomId: 1, userId: 1 } (unique)
   - `Application`: { corporateSponsorId: 1, currentMainStage: 1 }
   - `LoanOffer`: { applicationId: 1, status: 1 }

## Data Flow Diagram

```mermaid
sequenceDiagram
    participant SME as SME User
    participant APP as Application
    participant FP as Funding Programme
    participant WF as Workflow
    participant ASSESS as Assessment
    participant LOAN as Loan Management
    
    SME->>APP: Submit Application
    APP->>FP: Validate Eligibility
    FP-->>APP: Eligibility Confirmed
    APP->>WF: Initialize Workflow
    WF->>ASSESS: Trigger Assessments
    ASSESS->>ASSESS: Scorecard Evaluation
    ASSESS->>ASSESS: Schedule Interview
    ASSESS->>ASSESS: Site Visit
    ASSESS-->>WF: Assessment Complete
    WF->>LOAN: Generate Loan Offer
    LOAN-->>SME: Send Offer
    SME->>LOAN: Accept Offer
    LOAN->>LOAN: Create Loan Record
```

## Security and Access Control

### Role-Based Access

```mermaid
graph TD
    ADMIN[Admin Role]
    MANAGER[Manager Role]
    ANALYST[Analyst Role]
    SME_OWNER[SME Owner Role]
    SME_USER[SME User Role]
    
    ADMIN -->|Full Access| ALL[All Collections]
    MANAGER -->|Read/Write| APPS[Applications]
    MANAGER -->|Read/Write| ASSESS[Assessments]
    MANAGER -->|Read| LOANS[Loans]
    ANALYST -->|Read/Write| SCORE[Scorecards]
    ANALYST -->|Read| APPS
    SME_OWNER -->|Full Access| SME_DATA[Own SME Data]
    SME_OWNER -->|Read/Write| OWN_APPS[Own Applications]
    SME_USER -->|Read| SME_DATA
    SME_USER -->|Create| NEW_APPS[New Applications]
```

### Data Privacy Layers

1. **Public Data**: Corporate sponsor info, programme details
2. **Protected Data**: Application status, general statistics
3. **Confidential Data**: Personal info, financial details
4. **Restricted Data**: Loan details, risk assessments

## Performance Optimization Strategies

1. **Denormalization**
   - Store frequently accessed data in Application document
   - Cache user permissions in User document

2. **Aggregation Pipelines**
   - Pre-calculate statistics for dashboards
   - Use materialized views for reports

3. **Sharding Strategy**
   - Shard by corporateSponsorId for horizontal scaling
   - Separate collections for high-volume data (ChatMessages)

## Conclusion

This visual representation provides a comprehensive overview of the Screening Portal database structure. The schema supports:

- Multi-tenant architecture with corporate sponsors
- Complex application workflows
- Real-time communication
- Financial management
- Comprehensive assessment processes
- Role-based access control

The MongoDB document-based approach allows for flexible schema evolution while maintaining data integrity through application-level constraints and validation.