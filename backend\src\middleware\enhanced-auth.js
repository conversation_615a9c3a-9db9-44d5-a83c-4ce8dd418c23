const AccessControlService = require('../services/access-control.service');
const { normalizeObjectId } = require('../utils/object-id-utils');

/**
 * Enhanced entity filter middleware using centralized access control service
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const enhancedAddEntityFilter = async (req, res, next) => {
  console.log('🔍 ENHANCED ADD ENTITY FILTER - START');
  console.log('   - Route:', req.path);
  console.log('   - User exists:', !!req.user);
  console.log('   - User ID:', req.user?.id);
  console.log('   - User roles:', req.user?.roles);
  
  if (!req.user) {
    console.log('❌ ENHANCED ADD ENTITY FILTER - No user, calling next()');
    return next();
  }

  try {
    req.entityFilter = await AccessControlService.getUserEntityFilter(req.user);
    
    console.log('✅ ENHANCED ADD ENTITY FILTER - SUCCESS');
    console.log('   - Entity filter applied:', req.entityFilter);
    
    next();
  } catch (error) {
    console.error('❌ ENHANCED ADD ENTITY FILTER - ERROR:', error);
    return res.status(403).json({
      error: {
        code: 'ENTITY_FILTER_ERROR',
        message: error.message
      }
    });
  }
};

/**
 * Enhanced entity access validation middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const enhancedCheckEntityAccess = (req, res, next) => {
  console.log('🔍 ENHANCED CHECK ENTITY ACCESS - START');
  console.log('   - Route:', req.path);
  console.log('   - User exists:', !!req.user);
  console.log('   - User ID:', req.user?.id);
  
  if (!req.user) {
    console.log('❌ ENHANCED CHECK ENTITY ACCESS - No user, returning 401');
    return res.status(401).json({
      error: { code: 'UNAUTHORIZED', message: 'User not authenticated' }
    });
  }

  const isAdmin = AccessControlService.isAdmin(req.user);
  console.log('   - Is admin:', isAdmin);
  
  // Admin users have access to all entities
  if (isAdmin) {
    console.log('✅ ENHANCED CHECK ENTITY ACCESS - Admin user, calling next()');
    return next();
  }

  // Entity-specific users must have their entity associations
  if (AccessControlService.isCorporateSponsorUser(req.user)) {
    if (!req.user.corporateSponsorId && !req.user.organizationId) {
      return res.status(403).json({
        error: {
          code: 'MISSING_ENTITY_ASSOCIATION',
          message: 'Corporate sponsor user must have organization association'
        }
      });
    }
  }

  if (AccessControlService.isProgrammeUser(req.user)) {
    const programmeIds = AccessControlService.getUserProgrammeIds(req.user);
    if (programmeIds.length === 0) {
      return res.status(403).json({
        error: {
          code: 'MISSING_ENTITY_ASSOCIATION',
          message: 'Programme user must have programme assignments'
        }
      });
    }
  }

  if (AccessControlService.isSMEUser(req.user)) {
    if (!req.user.smeEntityId && !req.user.organizationId) {
      return res.status(403).json({
        error: {
          code: 'MISSING_SME_ENTITY_ASSOCIATION',
          message: 'SME user must have SME entity association'
        }
      });
    }
  }

  next();
};

/**
 * Validate entity association for all user types
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const validateEntityAssociation = async (req, res, next) => {
  console.log('🔍 VALIDATE ENTITY ASSOCIATION - START');
  console.log('   - Route:', req.path);
  console.log('   - User exists:', !!req.user);
  console.log('   - User ID:', req.user?.id);
  
  if (!req.user) {
    console.log('❌ VALIDATE ENTITY ASSOCIATION - No user, returning 401');
    return res.status(401).json({ 
      error: { code: 'UNAUTHORIZED', message: 'User not authenticated' }
    });
  }

  // Admin users bypass entity association validation
  if (AccessControlService.isAdmin(req.user)) {
    console.log('✅ VALIDATE ENTITY ASSOCIATION - Admin user, bypassing validation');
    return next();
  }

  console.log('🔍 VALIDATE ENTITY ASSOCIATION - Non-admin user, checking associations');
  const userRoles = req.user.roles || [];
  const organizationType = req.user.organizationType;
  console.log('   - User roles:', userRoles);
  console.log('   - Organization type:', organizationType);
  
  try {
    // Corporate sponsor users MUST have corporateSponsorId
    if (AccessControlService.isCorporateSponsorUser(req.user)) {
      if (!req.user.corporateSponsorId && !req.user.organizationId) {
        throw new Error('Corporate sponsor user missing organization association');
      }
    }
    
    // Programme users MUST have programme assignments
    if (AccessControlService.isProgrammeUser(req.user)) {
      const programmeIds = AccessControlService.getUserProgrammeIds(req.user);
      if (programmeIds.length === 0) {
        throw new Error('Programme user missing programme assignments');
      }
    }
    
    // SME users MUST have SME entity association
    if (AccessControlService.isSMEUser(req.user)) {
      if (!req.user.smeEntityId && !req.user.organizationId) {
        throw new Error('SME user missing SME entity association');
      }
      
      // Validate that the SME entity exists and user is authorized
      const SMEEntity = require('../models/sme-entity');
      const smeEntityId = req.user.smeEntityId || req.user.organizationId;
      const smeEntity = await SMEEntity.findById(smeEntityId);
      
      if (!smeEntity) {
        throw new Error('SME user associated with non-existent SME entity');
      }
      
      // Check if user is authorized for this SME entity (for SME users)
      if (req.user.userType === 'sme_user') {
        const isAuthorizedUser = smeEntity.users?.some(user => 
          user.userId?.toString() === req.user.userId?.toString()
        );
        
        if (!isAuthorizedUser) {
          throw new Error('SME user not authorized for associated SME entity');
        }
      }
      
      // Validate SME entity status
      if (smeEntity.status === 'inactive' || smeEntity.status === 'suspended') {
        throw new Error(`SME entity is ${smeEntity.status} - access denied`);
      }
    }
    
    next();
  } catch (error) {
    console.error('Entity association validation failed:', error.message);
    return res.status(403).json({
      error: {
        code: 'ENTITY_ASSOCIATION_ERROR',
        message: error.message
      }
    });
  }
};

/**
 * SME-specific entity ownership validation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const validateSMEEntityOwnership = async (req, res, next) => {
  if (!req.user || !AccessControlService.isSMEUser(req.user)) {
    return next(); // Not an SME user, skip validation
  }

  const requestedEntityId = req.params.id || req.body.smeEntityId;
  const userSMEEntityId = req.user.smeEntityId;

  if (!userSMEEntityId) {
    return res.status(403).json({
      error: {
        code: 'MISSING_SME_ENTITY_ASSOCIATION',
        message: 'SME user must have SME entity association'
      }
    });
  }

  if (requestedEntityId && requestedEntityId !== userSMEEntityId.toString()) {
    console.warn(`SME user ${req.user.id} attempted to access entity ${requestedEntityId} but owns ${userSMEEntityId}`);
    return res.status(403).json({
      error: {
        code: 'SME_ENTITY_ACCESS_DENIED',
        message: 'Access denied: You can only access your own SME entity'
      }
    });
  }

  next();
};

/**
 * Check SME entity access with enhanced validation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const checkSMEEntityAccess = (req, res, next) => {
  if (req.user && AccessControlService.isSMEUser(req.user)) {
    return validateSMEEntityOwnership(req, res, next);
  }
  next();
};

/**
 * SME application filtering middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const addSMEApplicationFilter = (req, res, next) => {
  if (!req.user) {
    return next();
  }

  // Initialize entity filter if not exists
  if (!req.entityFilter) {
    req.entityFilter = {};
  }

  // Admin users - no filtering (can see all applications)
  if (AccessControlService.isAdmin(req.user)) {
    return next();
  }

  // SME users - filter by their SME entity
  if (AccessControlService.isSMEUser(req.user)) {
    const smeEntityId = req.user.smeEntityId;
    if (!smeEntityId) {
      return res.status(403).json({
        error: {
          code: 'MISSING_SME_ENTITY_ASSOCIATION',
          message: 'SME user must have SME entity association to access applications'
        }
      });
    }
    
    // Filter applications by SME entity ownership
    req.entityFilter.smeEntityId = normalizeObjectId(smeEntityId);
    
    console.log(`SME Application Filter Applied: User ${req.user.id} can only see applications for SME entity ${smeEntityId}`);
  }

  next();
};

/**
 * Create secure route middleware chain for entity-specific routes
 */
const entitySecureRoute = [
  enhancedAddEntityFilter,
  enhancedCheckEntityAccess,
  validateEntityAssociation
];

/**
 * Create secure route middleware chain for SME-specific routes
 */
const smeSecureRoute = [
  enhancedAddEntityFilter,
  validateEntityAssociation,
  checkSMEEntityAccess,
  addSMEApplicationFilter
];

module.exports = {
  enhancedAddEntityFilter,
  enhancedCheckEntityAccess,
  validateEntityAssociation,
  validateSMEEntityOwnership,
  checkSMEEntityAccess,
  addSMEApplicationFilter,
  entitySecureRoute,
  smeSecureRoute
};
