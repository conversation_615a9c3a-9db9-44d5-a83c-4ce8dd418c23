const mongoose = require('mongoose');
require('dotenv').config();

async function assignAdminToEntity() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-screening-app');
    
    const CorporateSponsor = require('./src/models/corporate-sponsor');
    const User = require('./src/models/user');
    
    console.log('=== CREATING/FINDING 20/20INSIGHT ENTITY ===');
    
    // Check if 20/20Insight entity already exists
    let insightEntity = await CorporateSponsor.findOne({ 
      name: { $regex: '20/20.*insight', $options: 'i' } 
    });
    
    if (!insightEntity) {
      console.log('Creating 20/20Insight corporate sponsor entity...');
      insightEntity = new CorporateSponsor({
        name: '20/20Insight',
        organizationType: 'CorporateSponsor',
        description: 'System administration and oversight organization',
        contactPerson: {
          name: 'System Administrator',
          email: '<EMAIL>',
          phone: '+27-11-000-0000'
        },
        address: {
          street: '123 Admin Street',
          city: 'Johannesburg',
          province: 'Gauteng',
          postalCode: '2000',
          country: 'South Africa'
        },
        status: 'active',
        registrationNumber: 'ADMIN-001',
        taxNumber: 'TAX-ADMIN-001'
      });
      
      await insightEntity.save();
      console.log(`✅ Created 20/20Insight entity with ID: ${insightEntity._id}`);
    } else {
      console.log(`✅ Found existing 20/20Insight entity with ID: ${insightEntity._id}`);
    }
    
    console.log('\n=== UPDATING ADMIN USER ===');
    
    // Update admin user to be associated with 20/20Insight entity
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (adminUser) {
      console.log('Updating admin user with entity association...');
      
      adminUser.organizationId = insightEntity._id;
      adminUser.corporateSponsorId = insightEntity._id;
      adminUser.name = 'System Administrator'; // Set name if undefined
      
      await adminUser.save();
      
      console.log('✅ Admin user updated successfully!');
      console.log(`   - Organization ID: ${adminUser.organizationId}`);
      console.log(`   - Corporate Sponsor ID: ${adminUser.corporateSponsorId}`);
      console.log(`   - Name: ${adminUser.name}`);
    } else {
      console.log('❌ Admin user not found');
    }
    
    console.log('\n=== VERIFICATION ===');
    const updatedAdmin = await User.findOne({ email: '<EMAIL>' });
    console.log('Updated admin user:');
    console.log(`   - ID: ${updatedAdmin._id}`);
    console.log(`   - Name: ${updatedAdmin.name}`);
    console.log(`   - Email: ${updatedAdmin.email}`);
    console.log(`   - Roles: ${JSON.stringify(updatedAdmin.roles)}`);
    console.log(`   - Organization Type: ${updatedAdmin.organizationType}`);
    console.log(`   - Organization ID: ${updatedAdmin.organizationId}`);
    console.log(`   - Corporate Sponsor ID: ${updatedAdmin.corporateSponsorId}`);
    
    await mongoose.disconnect();
    console.log('\n🎉 Admin user successfully assigned to 20/20Insight entity!');
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

assignAdminToEntity();
